import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;

class IconPickerDialog extends StatefulWidget {
  final String selectedIcon;
  final String? selectedIconPath;

  const IconPickerDialog({
    super.key,
    required this.selectedIcon,
    this.selectedIconPath,
  });

  @override
  State<IconPickerDialog> createState() => _IconPickerDialogState();

  /// Static method to get IconData from icon name
  static IconData? getIconData(String iconName) {
    // Create a temporary instance to access the icon categories
    final categories = {
      'system': [
        {'name': 'settings', 'icon': Icons.settings},
        {'name': 'monitor', 'icon': Icons.monitor},
        {'name': 'memory', 'icon': Icons.memory},
        {'name': 'speed', 'icon': Icons.speed},
        {'name': 'power_settings_new', 'icon': Icons.power_settings_new},
        {'name': 'system_update', 'icon': Icons.system_update},
        {'name': 'hardware', 'icon': Icons.hardware},
        {'name': 'developer_board', 'icon': Icons.developer_board},
        {'name': 'sensors', 'icon': Icons.sensors},
        {'name': 'thermostat', 'icon': Icons.thermostat},
        {'name': 'computer', 'icon': Icons.computer},
        {'name': 'desktop_windows', 'icon': Icons.desktop_windows},
        {'name': 'laptop', 'icon': Icons.laptop},
        {'name': 'tablet', 'icon': Icons.tablet},
        {'name': 'smartphone', 'icon': Icons.smartphone},
        {'name': 'device_hub', 'icon': Icons.device_hub},
        {'name': 'devices', 'icon': Icons.devices},
        {'name': 'battery_full', 'icon': Icons.battery_full},
        {'name': 'battery_charging_full', 'icon': Icons.battery_charging_full},
        {'name': 'power', 'icon': Icons.power},
        {'name': 'electrical_services', 'icon': Icons.electrical_services},
        {'name': 'cpu', 'icon': Icons.memory},
        {'name': 'storage', 'icon': Icons.storage},
        {'name': 'sd_storage', 'icon': Icons.sd_storage},
        {'name': 'usb', 'icon': Icons.usb},
      ],
      'network': [
        {'name': 'network_check', 'icon': Icons.network_check},
        {'name': 'wifi', 'icon': Icons.wifi},
        {'name': 'router', 'icon': Icons.router},
        {'name': 'lan', 'icon': Icons.lan},
        {'name': 'signal_wifi_4_bar', 'icon': Icons.signal_wifi_4_bar},
        {'name': 'network_wifi', 'icon': Icons.network_wifi},
        {'name': 'public', 'icon': Icons.public},
        {'name': 'dns', 'icon': Icons.dns},
        {'name': 'vpn_lock', 'icon': Icons.vpn_lock},
        {'name': 'cloud', 'icon': Icons.cloud},
        {'name': 'cloud_download', 'icon': Icons.cloud_download},
        {'name': 'cloud_upload', 'icon': Icons.cloud_upload},
        {'name': 'cloud_sync', 'icon': Icons.cloud_sync},
        {'name': 'wifi_off', 'icon': Icons.wifi_off},
        {'name': 'signal_cellular_4_bar', 'icon': Icons.signal_cellular_4_bar},
        {'name': 'bluetooth', 'icon': Icons.bluetooth},
        {'name': 'bluetooth_connected', 'icon': Icons.bluetooth_connected},
        {'name': 'nfc', 'icon': Icons.nfc},
        {'name': 'cast', 'icon': Icons.cast},
        {'name': 'cast_connected', 'icon': Icons.cast_connected},
        {'name': 'share', 'icon': Icons.share},
        {'name': 'sync', 'icon': Icons.sync},
        {'name': 'sync_alt', 'icon': Icons.sync_alt},
        {'name': 'download', 'icon': Icons.download},
        {'name': 'upload', 'icon': Icons.upload},
        {'name': 'http', 'icon': Icons.http},
        {'name': 'https', 'icon': Icons.https},
        {'name': 'rss_feed', 'icon': Icons.rss_feed},
      ],
      'development': [
        {'name': 'code', 'icon': Icons.code},
        {'name': 'terminal', 'icon': Icons.terminal},
        {'name': 'bug_report', 'icon': Icons.bug_report},
        {'name': 'build', 'icon': Icons.build},
        {
          'name': 'integration_instructions',
          'icon': Icons.integration_instructions
        },
        {'name': 'api', 'icon': Icons.api},
        {'name': 'data_object', 'icon': Icons.data_object},
        {'name': 'webhook', 'icon': Icons.webhook},
        {'name': 'developer_mode', 'icon': Icons.developer_mode},
        {'name': 'source', 'icon': Icons.source},
        {'name': 'javascript', 'icon': Icons.code},
        {'name': 'html', 'icon': Icons.code},
        {'name': 'css', 'icon': Icons.palette},
        {'name': 'database', 'icon': Icons.storage},
        {'name': 'schema', 'icon': Icons.account_tree},
        {'name': 'functions', 'icon': Icons.functions},
        {'name': 'commit', 'icon': Icons.commit},
        {'name': 'merge_type', 'icon': Icons.merge_type},
        {'name': 'fork_right', 'icon': Icons.fork_right},
        {'name': 'git_hub', 'icon': Icons.code},
        {'name': 'version_control', 'icon': Icons.history},
        {'name': 'debug', 'icon': Icons.bug_report},
        {'name': 'test_tube', 'icon': Icons.science},
        {'name': 'rocket_launch', 'icon': Icons.rocket_launch},
        {'name': 'deploy', 'icon': Icons.publish},
        {'name': 'package', 'icon': Icons.inventory_2},
        {'name': 'library', 'icon': Icons.local_library},
        {'name': 'framework', 'icon': Icons.view_module},
        {'name': 'plugin', 'icon': Icons.extension},
        {'name': 'component', 'icon': Icons.widgets},
      ],
      'files': [
        {'name': 'folder', 'icon': Icons.folder},
        {'name': 'folder_open', 'icon': Icons.folder_open},
        {'name': 'file_copy', 'icon': Icons.file_copy},
        {'name': 'insert_drive_file', 'icon': Icons.insert_drive_file},
        {'name': 'description', 'icon': Icons.description},
        {'name': 'article', 'icon': Icons.article},
        {'name': 'storage', 'icon': Icons.storage},
        {'name': 'archive', 'icon': Icons.archive},
        {'name': 'backup', 'icon': Icons.backup},
        {'name': 'cloud_download', 'icon': Icons.cloud_download},
      ],
      'media': [
        {'name': 'camera', 'icon': Icons.camera},
        {'name': 'camera_alt', 'icon': Icons.camera_alt},
        {'name': 'videocam', 'icon': Icons.videocam},
        {'name': 'photo_camera', 'icon': Icons.photo_camera},
        {'name': 'image', 'icon': Icons.image},
        {'name': 'video_library', 'icon': Icons.video_library},
        {'name': 'photo_library', 'icon': Icons.photo_library},
        {'name': 'play_circle', 'icon': Icons.play_circle},
        {'name': 'movie', 'icon': Icons.movie},
        {'name': 'music_note', 'icon': Icons.music_note},
      ],
      'security': [
        {'name': 'security', 'icon': Icons.security},
        {'name': 'lock', 'icon': Icons.lock},
        {'name': 'shield', 'icon': Icons.shield},
        {'name': 'verified_user', 'icon': Icons.verified_user},
        {'name': 'admin_panel_settings', 'icon': Icons.admin_panel_settings},
        {'name': 'fingerprint', 'icon': Icons.fingerprint},
        {'name': 'key', 'icon': Icons.key},
        {'name': 'password', 'icon': Icons.password},
        {'name': 'gpp_good', 'icon': Icons.gpp_good},
        {'name': 'enhanced_encryption', 'icon': Icons.enhanced_encryption},
      ],
      'utilities': [
        {'name': 'apps', 'icon': Icons.apps},
        {'name': 'dashboard', 'icon': Icons.dashboard},
        {'name': 'widgets', 'icon': Icons.widgets},
        {'name': 'extension', 'icon': Icons.extension},
        {'name': 'tune', 'icon': Icons.tune},
        {'name': 'build_circle', 'icon': Icons.build_circle},
        {'name': 'handyman', 'icon': Icons.handyman},
        {'name': 'construction', 'icon': Icons.construction},
        {
          'name': 'precision_manufacturing',
          'icon': Icons.precision_manufacturing
        },
        {'name': 'engineering', 'icon': Icons.engineering},
        {'name': 'calculate', 'icon': Icons.calculate},
        {'name': 'timer', 'icon': Icons.timer},
        {'name': 'alarm', 'icon': Icons.alarm},
        {'name': 'schedule', 'icon': Icons.schedule},
        {'name': 'calendar_today', 'icon': Icons.calendar_today},
        {'name': 'event', 'icon': Icons.event},
        {'name': 'task_alt', 'icon': Icons.task_alt},
        {'name': 'checklist', 'icon': Icons.checklist},
        {'name': 'list_alt', 'icon': Icons.list_alt},
        {'name': 'note_add', 'icon': Icons.note_add},
        {'name': 'sticky_note_2', 'icon': Icons.sticky_note_2},
        {'name': 'bookmark', 'icon': Icons.bookmark},
        {'name': 'star', 'icon': Icons.star},
        {'name': 'favorite', 'icon': Icons.favorite},
        {'name': 'thumb_up', 'icon': Icons.thumb_up},
        {'name': 'trending_up', 'icon': Icons.trending_up},
        {'name': 'analytics', 'icon': Icons.analytics},
        {'name': 'insights', 'icon': Icons.insights},
        {'name': 'assessment', 'icon': Icons.assessment},
        {'name': 'bar_chart', 'icon': Icons.bar_chart},
        {'name': 'pie_chart', 'icon': Icons.pie_chart},
        {'name': 'show_chart', 'icon': Icons.show_chart},
      ],
      'ai_ml': [
        {'name': 'psychology', 'icon': Icons.psychology},
        {'name': 'smart_toy', 'icon': Icons.smart_toy},
        {'name': 'auto_awesome', 'icon': Icons.auto_awesome},
        {'name': 'lightbulb', 'icon': Icons.lightbulb},
        {'name': 'science', 'icon': Icons.science},
        {'name': 'biotech', 'icon': Icons.biotech},
        {
          'name': 'precision_manufacturing',
          'icon': Icons.precision_manufacturing
        },
        {'name': 'model_training', 'icon': Icons.model_training},
        {'name': 'neural_network', 'icon': Icons.account_tree},
        {'name': 'data_exploration', 'icon': Icons.explore},
        {'name': 'pattern_recognition', 'icon': Icons.pattern},
        {'name': 'algorithm', 'icon': Icons.functions},
        {'name': 'machine_learning', 'icon': Icons.psychology},
        {'name': 'artificial_intelligence', 'icon': Icons.smart_toy},
        {'name': 'deep_learning', 'icon': Icons.layers},
        {'name': 'computer_vision', 'icon': Icons.visibility},
        {'name': 'natural_language', 'icon': Icons.chat},
        {'name': 'voice_recognition', 'icon': Icons.mic},
        {'name': 'automation', 'icon': Icons.auto_mode},
        {'name': 'robot', 'icon': Icons.smart_toy},
        {'name': 'assistant', 'icon': Icons.assistant},
        {'name': 'chat_bot', 'icon': Icons.chat_bubble},
        {'name': 'prediction', 'icon': Icons.trending_up},
        {'name': 'optimization', 'icon': Icons.tune},
      ],
    };

    // Search through all categories for the icon
    for (final category in categories.values) {
      for (final iconData in category) {
        if (iconData['name'] == iconName) {
          return iconData['icon'] as IconData;
        }
      }
    }

    // Additional comprehensive icon mapping for common icons not in categories
    switch (iconName) {
      case 'sports_esports':
        return Icons.sports_esports;
      case 'games':
        return Icons.games;
      case 'casino':
        return Icons.casino;
      case 'videogame_asset':
        return Icons.videogame_asset;
      case 'gamepad':
        return Icons.gamepad;
      case 'web':
        return Icons.web;
      case 'email':
        return Icons.email;
      case 'message':
        return Icons.message;
      case 'phone':
        return Icons.phone;
      case 'person':
        return Icons.person;
      case 'group':
        return Icons.group;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'store':
        return Icons.store;
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'payment':
        return Icons.payment;
      case 'account_balance':
        return Icons.account_balance;
      case 'business':
        return Icons.business;
      case 'domain':
        return Icons.domain;
      case 'apartment':
        return Icons.apartment;
      case 'bed':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'pool':
        return Icons.pool;
      case 'spa':
        return Icons.spa;
      case 'golf_course':
        return Icons.golf_course;
      case 'palette':
        return Icons.palette;
      case 'brush':
        return Icons.brush;
      case 'format_paint':
        return Icons.format_paint;
      case 'color_lens':
        return Icons.color_lens;
      case 'gradient':
        return Icons.gradient;
      case 'eco':
        return Icons.eco;
      case 'park':
        return Icons.park;
      case 'nature':
        return Icons.nature;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'wb_cloudy':
        return Icons.wb_cloudy;
      case 'ac_unit':
        return Icons.ac_unit;
      case 'beach_access':
        return Icons.beach_access;
      case 'play_arrow':
        return Icons.play_arrow;
      case 'pause':
        return Icons.pause;
      case 'stop':
        return Icons.stop;
      case 'skip_next':
        return Icons.skip_next;
      case 'skip_previous':
        return Icons.skip_previous;
      case 'shuffle':
        return Icons.shuffle;
      case 'repeat':
        return Icons.repeat;
      case 'volume_up':
        return Icons.volume_up;
      case 'volume_down':
        return Icons.volume_down;
      case 'volume_off':
        return Icons.volume_off;
      case 'mic':
        return Icons.mic;
      case 'mic_off':
        return Icons.mic_off;
      case 'headphones':
        return Icons.headphones;
      case 'speaker':
        return Icons.speaker;
      case 'lock_open':
        return Icons.lock_open;
      case 'vpn_key':
        return Icons.vpn_key;
      default:
        return null;
    }
  }
}

class _IconPickerDialogState extends State<IconPickerDialog> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'all';
  bool _useCustomImage = false;
  String? _selectedImagePath;
  late String _currentSelectedIcon;

  @override
  void initState() {
    super.initState();
    // Initialize current selected icon
    _currentSelectedIcon = widget.selectedIcon;

    // Populate 'all' category with all icons
    _iconCategories['all'] =
        _iconCategories.values.expand((icons) => icons).toList();

    // Initialize with custom image if one was provided
    if (widget.selectedIconPath != null &&
        widget.selectedIconPath!.isNotEmpty) {
      _useCustomImage = true;
      _selectedImagePath = widget.selectedIconPath;
    }
  }

  final Map<String, List<Map<String, dynamic>>> _iconCategories = {
    'all': [],
    'system': [
      {'name': 'settings', 'icon': Icons.settings},
      {'name': 'monitor', 'icon': Icons.monitor},
      {'name': 'memory', 'icon': Icons.memory},
      {'name': 'speed', 'icon': Icons.speed},
      {'name': 'power_settings_new', 'icon': Icons.power_settings_new},
      {'name': 'system_update', 'icon': Icons.system_update},
      {'name': 'hardware', 'icon': Icons.hardware},
      {'name': 'developer_board', 'icon': Icons.developer_board},
      {'name': 'sensors', 'icon': Icons.sensors},
      {'name': 'thermostat', 'icon': Icons.thermostat},
      {'name': 'computer', 'icon': Icons.computer},
      {'name': 'desktop_windows', 'icon': Icons.desktop_windows},
      {'name': 'laptop', 'icon': Icons.laptop},
      {'name': 'tablet', 'icon': Icons.tablet},
      {'name': 'smartphone', 'icon': Icons.smartphone},
      {'name': 'device_hub', 'icon': Icons.device_hub},
      {'name': 'devices', 'icon': Icons.devices},
      {'name': 'battery_full', 'icon': Icons.battery_full},
      {'name': 'battery_charging_full', 'icon': Icons.battery_charging_full},
      {'name': 'power', 'icon': Icons.power},
      {'name': 'electrical_services', 'icon': Icons.electrical_services},
      {'name': 'cpu', 'icon': Icons.memory},
      {'name': 'storage', 'icon': Icons.storage},
      {'name': 'sd_storage', 'icon': Icons.sd_storage},
      {'name': 'usb', 'icon': Icons.usb},
    ],
    'network': [
      {'name': 'network_check', 'icon': Icons.network_check},
      {'name': 'wifi', 'icon': Icons.wifi},
      {'name': 'router', 'icon': Icons.router},
      {'name': 'lan', 'icon': Icons.lan},
      {'name': 'signal_wifi_4_bar', 'icon': Icons.signal_wifi_4_bar},
      {'name': 'network_wifi', 'icon': Icons.network_wifi},
      {'name': 'public', 'icon': Icons.public},
      {'name': 'dns', 'icon': Icons.dns},
      {'name': 'vpn_lock', 'icon': Icons.vpn_lock},
      {'name': 'cloud', 'icon': Icons.cloud},
      {'name': 'cloud_download', 'icon': Icons.cloud_download},
      {'name': 'cloud_upload', 'icon': Icons.cloud_upload},
      {'name': 'cloud_sync', 'icon': Icons.cloud_sync},
      {'name': 'wifi_off', 'icon': Icons.wifi_off},
      {'name': 'signal_cellular_4_bar', 'icon': Icons.signal_cellular_4_bar},
      {'name': 'bluetooth', 'icon': Icons.bluetooth},
      {'name': 'bluetooth_connected', 'icon': Icons.bluetooth_connected},
      {'name': 'nfc', 'icon': Icons.nfc},
      {'name': 'cast', 'icon': Icons.cast},
      {'name': 'cast_connected', 'icon': Icons.cast_connected},
      {'name': 'share', 'icon': Icons.share},
      {'name': 'sync', 'icon': Icons.sync},
      {'name': 'sync_alt', 'icon': Icons.sync_alt},
      {'name': 'download', 'icon': Icons.download},
      {'name': 'upload', 'icon': Icons.upload},
      {'name': 'http', 'icon': Icons.http},
      {'name': 'https', 'icon': Icons.https},
      {'name': 'rss_feed', 'icon': Icons.rss_feed},
    ],
    'development': [
      {'name': 'code', 'icon': Icons.code},
      {'name': 'terminal', 'icon': Icons.terminal},
      {'name': 'bug_report', 'icon': Icons.bug_report},
      {'name': 'build', 'icon': Icons.build},
      {
        'name': 'integration_instructions',
        'icon': Icons.integration_instructions
      },
      {'name': 'api', 'icon': Icons.api},
      {'name': 'data_object', 'icon': Icons.data_object},
      {'name': 'webhook', 'icon': Icons.webhook},
      {'name': 'developer_mode', 'icon': Icons.developer_mode},
      {'name': 'source', 'icon': Icons.source},
      {'name': 'javascript', 'icon': Icons.code},
      {'name': 'html', 'icon': Icons.code},
      {'name': 'css', 'icon': Icons.palette},
      {'name': 'database', 'icon': Icons.storage},
      {'name': 'schema', 'icon': Icons.account_tree},
      {'name': 'functions', 'icon': Icons.functions},
      {'name': 'commit', 'icon': Icons.commit},
      {'name': 'merge_type', 'icon': Icons.merge_type},
      {'name': 'fork_right', 'icon': Icons.fork_right},
      {'name': 'git_hub', 'icon': Icons.code},
      {'name': 'version_control', 'icon': Icons.history},
      {'name': 'debug', 'icon': Icons.bug_report},
      {'name': 'test_tube', 'icon': Icons.science},
      {'name': 'rocket_launch', 'icon': Icons.rocket_launch},
      {'name': 'deploy', 'icon': Icons.publish},
      {'name': 'package', 'icon': Icons.inventory_2},
      {'name': 'library', 'icon': Icons.local_library},
      {'name': 'framework', 'icon': Icons.view_module},
      {'name': 'plugin', 'icon': Icons.extension},
      {'name': 'component', 'icon': Icons.widgets},
    ],
    'files': [
      {'name': 'folder', 'icon': Icons.folder},
      {'name': 'folder_open', 'icon': Icons.folder_open},
      {'name': 'file_copy', 'icon': Icons.file_copy},
      {'name': 'insert_drive_file', 'icon': Icons.insert_drive_file},
      {'name': 'description', 'icon': Icons.description},
      {'name': 'article', 'icon': Icons.article},
      {'name': 'storage', 'icon': Icons.storage},
      {'name': 'archive', 'icon': Icons.archive},
      {'name': 'backup', 'icon': Icons.backup},
      {'name': 'cloud_download', 'icon': Icons.cloud_download},
    ],
    'media': [
      {'name': 'camera', 'icon': Icons.camera},
      {'name': 'camera_alt', 'icon': Icons.camera_alt},
      {'name': 'videocam', 'icon': Icons.videocam},
      {'name': 'photo_camera', 'icon': Icons.photo_camera},
      {'name': 'image', 'icon': Icons.image},
      {'name': 'video_library', 'icon': Icons.video_library},
      {'name': 'photo_library', 'icon': Icons.photo_library},
      {'name': 'play_circle', 'icon': Icons.play_circle},
      {'name': 'movie', 'icon': Icons.movie},
      {'name': 'music_note', 'icon': Icons.music_note},
    ],
    'security': [
      {'name': 'security', 'icon': Icons.security},
      {'name': 'lock', 'icon': Icons.lock},
      {'name': 'shield', 'icon': Icons.shield},
      {'name': 'verified_user', 'icon': Icons.verified_user},
      {'name': 'admin_panel_settings', 'icon': Icons.admin_panel_settings},
      {'name': 'fingerprint', 'icon': Icons.fingerprint},
      {'name': 'key', 'icon': Icons.key},
      {'name': 'password', 'icon': Icons.password},
      {'name': 'gpp_good', 'icon': Icons.gpp_good},
      {'name': 'enhanced_encryption', 'icon': Icons.enhanced_encryption},
    ],
    'games': [
      {'name': 'sports_esports', 'icon': Icons.sports_esports},
      {'name': 'games', 'icon': Icons.games},
      {'name': 'casino', 'icon': Icons.casino},
      {'name': 'sports_soccer', 'icon': Icons.sports_soccer},
      {'name': 'sports_basketball', 'icon': Icons.sports_basketball},
      {'name': 'sports_football', 'icon': Icons.sports_football},
      {'name': 'sports_baseball', 'icon': Icons.sports_baseball},
      {'name': 'sports_tennis', 'icon': Icons.sports_tennis},
      {'name': 'sports_golf', 'icon': Icons.sports_golf},
      {'name': 'sports_hockey', 'icon': Icons.sports_hockey},
      {'name': 'videogame_asset', 'icon': Icons.videogame_asset},
      {'name': 'gamepad', 'icon': Icons.gamepad},
    ],
    'utilities': [
      {'name': 'apps', 'icon': Icons.apps},
      {'name': 'dashboard', 'icon': Icons.dashboard},
      {'name': 'widgets', 'icon': Icons.widgets},
      {'name': 'extension', 'icon': Icons.extension},
      {'name': 'tune', 'icon': Icons.tune},
      {'name': 'build_circle', 'icon': Icons.build_circle},
      {'name': 'handyman', 'icon': Icons.handyman},
      {'name': 'construction', 'icon': Icons.construction},
      {
        'name': 'precision_manufacturing',
        'icon': Icons.precision_manufacturing
      },
      {'name': 'engineering', 'icon': Icons.engineering},
      {'name': 'calculate', 'icon': Icons.calculate},
      {'name': 'timer', 'icon': Icons.timer},
      {'name': 'alarm', 'icon': Icons.alarm},
      {'name': 'schedule', 'icon': Icons.schedule},
      {'name': 'calendar_today', 'icon': Icons.calendar_today},
      {'name': 'event', 'icon': Icons.event},
      {'name': 'task_alt', 'icon': Icons.task_alt},
      {'name': 'checklist', 'icon': Icons.checklist},
      {'name': 'list_alt', 'icon': Icons.list_alt},
      {'name': 'note_add', 'icon': Icons.note_add},
      {'name': 'sticky_note_2', 'icon': Icons.sticky_note_2},
      {'name': 'bookmark', 'icon': Icons.bookmark},
      {'name': 'star', 'icon': Icons.star},
      {'name': 'favorite', 'icon': Icons.favorite},
      {'name': 'thumb_up', 'icon': Icons.thumb_up},
      {'name': 'trending_up', 'icon': Icons.trending_up},
      {'name': 'analytics', 'icon': Icons.analytics},
      {'name': 'insights', 'icon': Icons.insights},
      {'name': 'assessment', 'icon': Icons.assessment},
      {'name': 'bar_chart', 'icon': Icons.bar_chart},
      {'name': 'pie_chart', 'icon': Icons.pie_chart},
      {'name': 'show_chart', 'icon': Icons.show_chart},
    ],
    'ai_ml': [
      {'name': 'psychology', 'icon': Icons.psychology},
      {'name': 'smart_toy', 'icon': Icons.smart_toy},
      {'name': 'auto_awesome', 'icon': Icons.auto_awesome},
      {'name': 'lightbulb', 'icon': Icons.lightbulb},
      {'name': 'science', 'icon': Icons.science},
      {'name': 'biotech', 'icon': Icons.biotech},
      {
        'name': 'precision_manufacturing',
        'icon': Icons.precision_manufacturing
      },
      {'name': 'model_training', 'icon': Icons.model_training},
      {'name': 'neural_network', 'icon': Icons.account_tree},
      {'name': 'data_exploration', 'icon': Icons.explore},
      {'name': 'pattern_recognition', 'icon': Icons.pattern},
      {'name': 'algorithm', 'icon': Icons.functions},
      {'name': 'machine_learning', 'icon': Icons.psychology},
      {'name': 'artificial_intelligence', 'icon': Icons.smart_toy},
      {'name': 'deep_learning', 'icon': Icons.layers},
      {'name': 'computer_vision', 'icon': Icons.visibility},
      {'name': 'natural_language', 'icon': Icons.chat},
      {'name': 'voice_recognition', 'icon': Icons.mic},
      {'name': 'automation', 'icon': Icons.auto_mode},
      {'name': 'robot', 'icon': Icons.smart_toy},
      {'name': 'assistant', 'icon': Icons.assistant},
      {'name': 'chat_bot', 'icon': Icons.chat_bubble},
      {'name': 'prediction', 'icon': Icons.trending_up},
      {'name': 'optimization', 'icon': Icons.tune},
    ],
  };

  // Removed duplicate initState - using the one above

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get _filteredIcons {
    final icons = _iconCategories[_selectedCategory] ?? [];
    if (_searchQuery.isEmpty) return icons;

    return icons.where((icon) {
      return icon['name']
          .toString()
          .toLowerCase()
          .contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Future<void> _pickCustomImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedImagePath = result.files.single.path;
          _useCustomImage = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _returnResult() {
    if (_useCustomImage && _selectedImagePath != null) {
      // Return both the icon name (for fallback) and the image path
      Navigator.of(context).pop({
        'iconName': 'custom',
        'iconPath': _selectedImagePath,
      });
    } else {
      // Return just the icon name for material icons
      // Use the currently selected icon, not the original one
      Navigator.of(context).pop(_currentSelectedIcon);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Choose Icon'),
      content: SizedBox(
        width: 500,
        height: 600,
        child: Column(
          children: [
            // Icon type toggle
            Row(
              children: [
                Expanded(
                  child: FilterChip(
                    label: Text(
                      'Material Icons',
                      style: TextStyle(
                        color: !_useCustomImage ? Colors.white : null,
                        fontWeight: !_useCustomImage ? FontWeight.w500 : null,
                      ),
                    ),
                    selected: !_useCustomImage,
                    selectedColor: const Color(0xFF6366F1),
                    checkmarkColor: Colors.white,
                    backgroundColor: Colors.grey[100],
                    side: BorderSide(
                      color: !_useCustomImage
                          ? const Color(0xFF6366F1)
                          : Colors.grey[300]!,
                      width: 1,
                    ),
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _useCustomImage = false;
                          _selectedImagePath = null;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: FilterChip(
                    label: Text(
                      'Custom Image',
                      style: TextStyle(
                        color: _useCustomImage ? Colors.white : null,
                        fontWeight: _useCustomImage ? FontWeight.w500 : null,
                      ),
                    ),
                    selected: _useCustomImage,
                    selectedColor: const Color(0xFF6366F1),
                    checkmarkColor: Colors.white,
                    backgroundColor: Colors.grey[100],
                    side: BorderSide(
                      color: _useCustomImage
                          ? const Color(0xFF6366F1)
                          : Colors.grey[300]!,
                      width: 1,
                    ),
                    onSelected: (selected) {
                      setState(() {
                        _useCustomImage = selected;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (!_useCustomImage) ...[
              // Search bar for material icons
              TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'Search icons',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Category filter
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _iconCategories.keys.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(
                          category.toUpperCase(),
                          style: TextStyle(
                            color: isSelected ? Colors.white : null,
                            fontWeight: isSelected ? FontWeight.w500 : null,
                          ),
                        ),
                        selected: isSelected,
                        selectedColor: const Color(0xFF6366F1),
                        checkmarkColor: Colors.white,
                        backgroundColor: Colors.grey[100],
                        side: BorderSide(
                          color: isSelected
                              ? const Color(0xFF6366F1)
                              : Colors.grey[300]!,
                          width: 1,
                        ),
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 16),

              // Icon grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 6,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _filteredIcons.length,
                  itemBuilder: (context, index) {
                    final iconData = _filteredIcons[index];
                    final iconName = iconData['name'] as String;
                    final icon = iconData['icon'] as IconData;
                    final isSelected = iconName == _currentSelectedIcon;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          _currentSelectedIcon = iconName;
                        });
                        Navigator.of(context).pop(iconName);
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: isSelected
                              ? Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withValues(alpha: 0.1)
                              : null,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              icon,
                              size: 24,
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey[700],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              iconName.split('_').first,
                              style: TextStyle(
                                fontSize: 10,
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ] else ...[
              // Custom image section
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_selectedImagePath != null) ...[
                      // Show selected image preview
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            File(_selectedImagePath!),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.error,
                                size: 48,
                                color: Colors.red,
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Selected Image',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _selectedImagePath!.split('/').last,
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                    ] else ...[
                      const Icon(
                        Icons.image,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No image selected',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 24),
                    ],
                    ElevatedButton.icon(
                      onPressed: _pickCustomImage,
                      icon: const Icon(Icons.folder_open),
                      label: Text(_selectedImagePath != null
                          ? 'Change Image'
                          : 'Select Image'),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        if (_useCustomImage && _selectedImagePath != null)
          FilledButton(
            onPressed: _returnResult,
            child: const Text('Use Image'),
          ),
      ],
    );
  }
}
