import 'package:flutter/material.dart';
import 'dart:io';
import '../services/app_creator_service.dart';
import '../shared/shared_service_registry.dart';

class CreateAppDialog extends StatefulWidget {
  const CreateAppDialog({super.key});

  @override
  State<CreateAppDialog> createState() => _CreateAppDialogState();
}

class _CreateAppDialogState extends State<CreateAppDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _authorController = TextEditingController();
  final _versionController = TextEditingController(text: '1.0.0');

  String _selectedCategory = 'utilities';
  String _selectedIcon = 'apps';
  String? _selectedIconPath;
  String _selectedTemplate = 'basic';
  bool _isLoading = false;
  bool _raspberryPiOnly = false;

  final List<String> _categories = [
    'system',
    'utilities',
    'network',
    'development',
    'media',
    'security',
    'custom'
  ];

  final List<Map<String, String>> _templates = [
    {
      'id': 'basic',
      'name': 'Basic App',
      'description': 'Simple app with basic UI components',
      'details':
          'Perfect for: Simple utilities, calculators, converters, basic tools',
      'features': 'Basic UI, settings, simple interactions'
    },
    {
      'id': 'system_tool',
      'name': 'System Tool',
      'description': 'App that interacts with system resources',
      'details':
          'Perfect for: System monitors, process managers, hardware controllers',
      'features':
          'System access, device connection required, hardware interaction'
    },
    {
      'id': 'network_tool',
      'name': 'Network Tool',
      'description': 'App for network operations and diagnostics',
      'details':
          'Perfect for: Network scanners, ping tools, bandwidth monitors',
      'features': 'Network access, connection testing, remote operations'
    },
    {
      'id': 'data_viewer',
      'name': 'Data Viewer',
      'description': 'App for displaying and analyzing data',
      'details': 'Perfect for: Log viewers, chart displays, data dashboards',
      'features': 'Data visualization, charts, tables, export functionality'
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _authorController.dispose();
    _versionController.dispose();
    super.dispose();
  }

  String _formatCategoryName(String category) {
    return category
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.create, color: Color(0xFF6366F1)),
          SizedBox(width: 12),
          Text('Create New App'),
        ],
      ),
      content: SizedBox(
        width: 500,
        height: 700,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create a new app from a template',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),

                // App Name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'App Name *',
                    hintText: 'My Awesome App',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'App name is required';
                    }
                    if (value.trim().length < 3) {
                      return 'App name must be at least 3 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Brief description of your app',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Description is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Author
                TextFormField(
                  controller: _authorController,
                  decoration: const InputDecoration(
                    labelText: 'Author',
                    hintText: 'Your name or organization',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                ),
                const SizedBox(height: 16),

                // Version
                TextFormField(
                  controller: _versionController,
                  decoration: const InputDecoration(
                    labelText: 'Version',
                    hintText: '1.0.0',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.tag),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Version is required';
                    }
                    // Basic version format validation
                    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
                    if (!versionRegex.hasMatch(value.trim())) {
                      return 'Version must be in format x.y.z (e.g., 1.0.0)';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Category
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(_formatCategoryName(category)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Icon
                InkWell(
                  onTap: _showIconPicker,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Icon',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.image),
                    ),
                    child: Row(
                      children: [
                        if (_selectedIconPath != null) ...[
                          // Show custom image
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: Image.file(
                                File(_selectedIconPath!),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.error,
                                    size: 16,
                                    color: Colors.red,
                                  );
                                },
                              ),
                            ),
                          ),
                        ] else ...[
                          // Show material icon
                          Icon(
                            SharedServiceRegistry()
                                    .getIconData(_selectedIcon) ??
                                Icons.apps,
                            size: 20,
                            color: const Color(0xFF6366F1),
                          ),
                        ],
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedIconPath != null
                                ? 'Custom Image: ${_selectedIconPath!.split('/').last}'
                                : _selectedIcon
                                    .replaceAll('_', ' ')
                                    .toUpperCase(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Raspberry Pi Only checkbox
                Card(
                  elevation: 1,
                  color: const Color(0xFFFAFBFC),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: CheckboxListTile(
                    contentPadding: const EdgeInsets.all(16),
                    title: const Row(
                      children: [
                        Icon(Icons.memory, color: Color(0xFF6366F1), size: 20),
                        SizedBox(width: 8),
                        Text(
                          'Raspberry Pi Only',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                      ],
                    ),
                    subtitle: const Text(
                      'This app will only be visible when connected to a Raspberry Pi device',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                    value: _raspberryPiOnly,
                    activeColor: const Color(0xFF6366F1),
                    onChanged: (value) {
                      setState(() {
                        _raspberryPiOnly = value ?? false;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // Template
                const Text(
                  'Template',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ..._templates.map((template) {
                  final isSelected = template['id'] == _selectedTemplate;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Card(
                      elevation: isSelected ? 3 : 1,
                      color: const Color(0xFFFAFBFC),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: isSelected
                              ? const Color(0xFF6366F1)
                              : Colors.grey.withValues(alpha: 0.2),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: RadioListTile<String>(
                        contentPadding: const EdgeInsets.all(16),
                        title: Text(
                          template['name']!,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: isSelected
                                ? const Color(0xFF6366F1)
                                : const Color(0xFF1F2937),
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text(
                              template['description']!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF6366F1)
                                      .withValues(alpha: 0.05),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: const Color(0xFF6366F1)
                                        .withValues(alpha: 0.2),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      template['details']!,
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: Color(0xFF4B5563),
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      'Features: ${template['features']!}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF6B7280),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                        value: template['id']!,
                        groupValue: _selectedTemplate,
                        activeColor: const Color(0xFF6366F1),
                        onChanged: (value) {
                          setState(() {
                            _selectedTemplate = value!;
                          });
                        },
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading ? null : _createApp,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create'),
        ),
      ],
    );
  }

  void _showIconPicker() async {
    final result = await SharedServiceRegistry().showIconPicker(
      context,
      selectedIcon: _selectedIcon,
      selectedIconPath: _selectedIconPath,
    );

    if (result != null) {
      setState(() {
        _selectedIcon = result['iconName'] ?? 'apps';
        _selectedIconPath = result['iconPath'];
      });
    }
  }

  Future<void> _createApp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final appCreator = AppCreatorService();
      await appCreator.createApp(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        author: _authorController.text.trim().isEmpty
            ? 'Unknown'
            : _authorController.text.trim(),
        version: _versionController.text.trim(),
        category: _selectedCategory,
        icon: _selectedIcon,
        template: _selectedTemplate,
        iconPath: _selectedIconPath,
        raspberryPiOnly: _raspberryPiOnly,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App created successfully! Check the Apps page.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating app: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
