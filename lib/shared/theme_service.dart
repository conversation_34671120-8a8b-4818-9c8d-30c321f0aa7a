import 'package:flutter/material.dart';

/// Shared service for theme utilities
/// Provides consistent theming and color utilities across the app
class ThemeService {
  /// Get the current color scheme
  static ColorScheme getColorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }

  /// Get status colors for different states
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
      case 'connected':
      case 'running':
      case 'active':
      case 'enabled':
      case 'success':
        return const Color(0xFF10B981); // Green
      case 'offline':
      case 'disconnected':
      case 'stopped':
      case 'inactive':
      case 'disabled':
      case 'error':
      case 'failed':
        return const Color(0xFFEF4444); // Red
      case 'warning':
      case 'pending':
      case 'updating':
      case 'loading':
        return const Color(0xFFF59E0B); // Amber
      case 'info':
      case 'unknown':
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }

  /// Get action colors with softer, more pleasant tones
  static Map<String, Color> getActionColors() {
    return {
      'install': const Color(0xFF10B981), // Emerald
      'update': const Color(0xFFF59E0B),  // Amber
      'remove': const Color(0xFFEF4444),  // Red
      'primary': const Color(0xFF6366F1), // Purple
      'secondary': const Color(0xFF8B5CF6), // Violet
    };
  }

  /// Create a gradient for containers
  static LinearGradient createGradient(Color baseColor, {double opacity = 0.7}) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        baseColor,
        baseColor.withValues(alpha: opacity),
      ],
    );
  }

  /// Get terminal color schemes
  static Map<String, Map<String, Color>> getTerminalThemes() {
    return {
      'Ocean Dark': {
        'background': const Color(0xFF0F1419),
        'foreground': const Color(0xFFCCCCCC),
        'cursor': const Color(0xFFCCCCCC),
        'selection': const Color(0xFF264F78),
        'bold': const Color(0xFFFFFFFF),
        'black': const Color(0xFF000000),
        'red': const Color(0xFFFF3333),
        'green': const Color(0xFF86B300),
        'yellow': const Color(0xFFF29718),
        'blue': const Color(0xFF41A6D9),
        'magenta': const Color(0xFFFF8080),
        'cyan': const Color(0xFF4CBF99),
        'white': const Color(0xFFCCCCCC),
      },
      'Midnight': {
        'background': const Color(0xFF1A1A2E),
        'foreground': const Color(0xFFE94560),
        'cursor': const Color(0xFFE94560),
        'selection': const Color(0xFF16213E),
        'bold': const Color(0xFFFFFFFF),
        'black': const Color(0xFF000000),
        'red': const Color(0xFFE94560),
        'green': const Color(0xFF0F3460),
        'yellow': const Color(0xFFF5F5F5),
        'blue': const Color(0xFF533483),
        'magenta': const Color(0xFFE94560),
        'cyan': const Color(0xFF0F3460),
        'white': const Color(0xFFF5F5F5),
      },
      'Solarized Dark': {
        'background': const Color(0xFF002B36),
        'foreground': const Color(0xFF839496),
        'cursor': const Color(0xFF839496),
        'selection': const Color(0xFF073642),
        'bold': const Color(0xFFFDF6E3),
        'black': const Color(0xFF073642),
        'red': const Color(0xFFDC322F),
        'green': const Color(0xFF859900),
        'yellow': const Color(0xFFB58900),
        'blue': const Color(0xFF268BD2),
        'magenta': const Color(0xFFD33682),
        'cyan': const Color(0xFF2AA198),
        'white': const Color(0xFFEEE8D5),
      },
    };
  }

  /// Get app theme colors
  static Map<String, Color> getAppThemeColors() {
    return {
      'primary': const Color(0xFF6366F1),
      'primaryContainer': const Color(0xFFEEF2FF),
      'secondary': const Color(0xFF10B981),
      'secondaryContainer': const Color(0xFFD1FAE5),
      'surface': const Color(0xFFF8FAFC),
      'surfaceContainerHighest': const Color(0xFFFFFFFF),
      'error': const Color(0xFFEF4444),
      'onPrimary': const Color(0xFFFFFFFF),
      'onSecondary': const Color(0xFFFFFFFF),
      'onSurface': const Color(0xFF1F2937),
      'onError': const Color(0xFFFFFFFF),
      'outline': const Color(0xFFD1D5DB),
    };
  }

  /// Create a card decoration with theme-appropriate styling
  static BoxDecoration createCardDecoration(BuildContext context, {
    bool isSelected = false,
    Color? customColor,
  }) {
    final colorScheme = getColorScheme(context);
    
    return BoxDecoration(
      color: customColor ?? (isSelected 
          ? colorScheme.primaryContainer.withValues(alpha: 0.3)
          : colorScheme.surface),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: isSelected 
            ? colorScheme.primary.withValues(alpha: 0.5)
            : colorScheme.outline.withValues(alpha: 0.2),
        width: isSelected ? 2 : 1,
      ),
      boxShadow: isSelected ? [
        BoxShadow(
          color: colorScheme.primary.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ] : null,
    );
  }

  /// Create a button style with consistent theming
  static ButtonStyle createButtonStyle(BuildContext context, {
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    Size? minimumSize,
  }) {
    final colorScheme = getColorScheme(context);
    
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? colorScheme.primary,
      foregroundColor: foregroundColor ?? colorScheme.onPrimary,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      minimumSize: minimumSize ?? const Size(88, 36),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
    );
  }

  /// Get icon color based on theme and status
  static Color getIconColor(BuildContext context, {String? status}) {
    final colorScheme = getColorScheme(context);
    
    if (status != null) {
      return getStatusColor(status);
    }
    
    return colorScheme.onSurface;
  }

  /// Create a text style with theme-appropriate colors
  static TextStyle createTextStyle(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    String? fontFamily,
  }) {
    final theme = Theme.of(context);
    
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? theme.colorScheme.onSurface,
      fontFamily: fontFamily,
    );
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth > 1200) {
      return const EdgeInsets.all(24);
    } else if (screenWidth > 800) {
      return const EdgeInsets.all(16);
    } else {
      return const EdgeInsets.all(12);
    }
  }

  /// Get responsive grid column count
  static int getResponsiveColumns(double screenWidth, {
    int maxColumns = 10,
    double minCardWidth = 200,
  }) {
    final availableColumns = (screenWidth / minCardWidth).floor();
    return availableColumns.clamp(1, maxColumns);
  }
}
