import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'icon_picker_service.dart';
import 'file_operations_service.dart';
import 'dialog_service.dart';
import 'theme_service.dart';

/// Registry for shared services and components that can be used across multiple apps
class SharedServiceRegistry {
  static final SharedServiceRegistry _instance =
      SharedServiceRegistry._internal();
  factory SharedServiceRegistry() => _instance;
  SharedServiceRegistry._internal();

  // Service instances
  late final IconPickerService _iconPickerService;
  late final FileOperationsService _fileOperationsService;

  /// Initialize all shared services
  void initialize() {
    _iconPickerService = IconPickerService();
    _fileOperationsService = FileOperationsService();
  }

  /// Get the icon picker service
  IconPickerService get iconPicker => _iconPickerService;

  /// Get the file operations service
  FileOperationsService get fileOperations => _fileOperationsService;

  /// Show icon picker dialog - main entry point for all apps
  Future<Map<String, dynamic>?> showIconPicker(
    BuildContext context, {
    String? selectedIcon,
    String? selectedIconPath,
  }) async {
    return await _iconPickerService.showIconPicker(
      context,
      selectedIcon: selectedIcon,
      selectedIconPath: selectedIconPath,
    );
  }

  /// Get icon data from icon name - utility method
  IconData? getIconData(String? iconName) {
    return _iconPickerService.getIconData(iconName);
  }

  /// Build icon widget from app data - utility method
  Widget buildIconWidget(
    String? iconName,
    String? iconPath, {
    double size = 24,
    Color? color,
  }) {
    return _iconPickerService.buildIconWidget(
      iconName,
      iconPath,
      size: size,
      color: color,
    );
  }

  // File Operations convenience methods
  /// Pick a single file
  Future<String?> pickFile({
    String? dialogTitle,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    return await _fileOperationsService.pickFile(
      dialogTitle: dialogTitle,
      type: type,
      allowedExtensions: allowedExtensions,
    );
  }

  /// Pick an image file
  Future<String?> pickImage({String? dialogTitle}) async {
    return await _fileOperationsService.pickImage(dialogTitle: dialogTitle);
  }

  /// Save a file
  Future<String?> saveFile({
    String? dialogTitle,
    String? fileName,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    return await _fileOperationsService.saveFile(
      dialogTitle: dialogTitle,
      fileName: fileName,
      type: type,
      allowedExtensions: allowedExtensions,
    );
  }

  /// Pick a directory
  Future<String?> pickDirectory({String? dialogTitle}) async {
    return await _fileOperationsService.pickDirectory(dialogTitle: dialogTitle);
  }

  // Dialog convenience methods
  /// Show confirmation dialog
  static Future<bool> showConfirmation(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    Color? confirmColor,
    String? warning,
    IconData? icon,
  }) async {
    return await DialogService.showConfirmation(
      context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      confirmColor: confirmColor,
      warning: warning,
      icon: icon,
    );
  }

  /// Show delete confirmation dialog
  static Future<bool> showDeleteConfirmation(
    BuildContext context, {
    required String itemName,
    String? warning,
  }) async {
    return await DialogService.showDeleteConfirmation(
      context,
      itemName: itemName,
      warning: warning,
    );
  }

  /// Show error dialog
  static Future<void> showError(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'OK',
  }) async {
    await DialogService.showError(
      context,
      title: title,
      message: message,
      buttonText: buttonText,
    );
  }

  // Theme convenience methods
  /// Get status color
  static Color getStatusColor(String status) {
    return ThemeService.getStatusColor(status);
  }

  /// Get action colors
  static Map<String, Color> getActionColors() {
    return ThemeService.getActionColors();
  }

  /// Create card decoration
  static BoxDecoration createCardDecoration(
    BuildContext context, {
    bool isSelected = false,
    Color? customColor,
  }) {
    return ThemeService.createCardDecoration(
      context,
      isSelected: isSelected,
      customColor: customColor,
    );
  }
}
