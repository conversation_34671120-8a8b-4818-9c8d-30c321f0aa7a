import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

/// Shared service for file operations
/// Provides unified interface for file picking, saving, and directory operations
class FileOperationsService {
  /// Pick a single file
  Future<String?> pickFile({
    String? dialogTitle,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowMultiple: false,
        dialogTitle: dialogTitle,
        allowedExtensions: allowedExtensions,
      );

      return result?.files.single.path;
    } catch (e) {
      debugPrint('Error picking file: $e');
      return null;
    }
  }

  /// Pick multiple files
  Future<List<String>> pickFiles({
    String? dialogTitle,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowMultiple: true,
        dialogTitle: dialogTitle,
        allowedExtensions: allowedExtensions,
      );

      return result?.files
          .where((file) => file.path != null)
          .map((file) => file.path!)
          .toList() ?? [];
    } catch (e) {
      debugPrint('Error picking files: $e');
      return [];
    }
  }

  /// Pick an image file
  Future<String?> pickImage({String? dialogTitle}) async {
    return await pickFile(
      dialogTitle: dialogTitle ?? 'Select Image',
      type: FileType.image,
    );
  }

  /// Save a file
  Future<String?> saveFile({
    String? dialogTitle,
    String? fileName,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      return await FilePicker.platform.saveFile(
        dialogTitle: dialogTitle,
        fileName: fileName,
        type: type,
        allowedExtensions: allowedExtensions,
      );
    } catch (e) {
      debugPrint('Error saving file: $e');
      return null;
    }
  }

  /// Pick a directory
  Future<String?> pickDirectory({String? dialogTitle}) async {
    try {
      return await FilePicker.platform.getDirectoryPath(
        dialogTitle: dialogTitle,
      );
    } catch (e) {
      debugPrint('Error picking directory: $e');
      return null;
    }
  }

  /// Check if a file exists
  bool fileExists(String path) {
    try {
      return File(path).existsSync();
    } catch (e) {
      return false;
    }
  }

  /// Check if a directory exists
  bool directoryExists(String path) {
    try {
      return Directory(path).existsSync();
    } catch (e) {
      return false;
    }
  }

  /// Get file size in bytes
  int? getFileSize(String path) {
    try {
      return File(path).lengthSync();
    } catch (e) {
      return null;
    }
  }

  /// Get human-readable file size
  String getHumanReadableSize(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var size = bytes.toDouble();
    var suffixIndex = 0;

    while (size >= 1024 && suffixIndex < suffixes.length - 1) {
      size /= 1024;
      suffixIndex++;
    }

    return '${size.toStringAsFixed(size < 10 ? 1 : 0)} ${suffixes[suffixIndex]}';
  }

  /// Generate a unique filename by adding a number suffix if file exists
  String generateUniqueFilename(String basePath) {
    if (!fileExists(basePath)) return basePath;

    final file = File(basePath);
    final directory = file.parent.path;
    final nameWithoutExtension = file.path.split('.').first.split('/').last;
    final extension = file.path.contains('.') ? '.${file.path.split('.').last}' : '';

    int counter = 1;
    String newPath;
    do {
      newPath = '$directory/$nameWithoutExtension ($counter)$extension';
      counter++;
    } while (fileExists(newPath));

    return newPath;
  }
}
