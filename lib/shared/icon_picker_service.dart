import 'package:flutter/material.dart';
import 'dart:io';
import '../widgets/icon_picker_dialog.dart';

/// Shared service for icon picking functionality
/// This service provides a unified interface for icon selection across all apps
class IconPickerService {
  /// Show the icon picker dialog
  Future<Map<String, dynamic>?> showIconPicker(
    BuildContext context, {
    String? selectedIcon,
    String? selectedIconPath,
  }) async {
    final result = await showDialog<dynamic>(
      context: context,
      builder: (context) => IconPickerDialog(
        selectedIcon: selectedIcon ?? 'apps',
        selectedIconPath: selectedIconPath,
      ),
    );

    if (result != null) {
      if (result is Map<String, dynamic>) {
        // Custom image selected
        return {
          'iconName': result['iconName'] ?? 'custom',
          'iconPath': result['iconPath'],
          'isCustom': true,
        };
      } else if (result is String) {
        // Material icon selected
        return {
          'iconName': result,
          'iconPath': null,
          'isCustom': false,
        };
      }
    }
    return null;
  }

  /// Get IconData from icon name
  IconData? getIconData(String? iconName) {
    if (iconName == null) return null;
    return IconPickerDialog.getIconData(iconName);
  }

  /// Build an icon widget that handles both material icons and custom images
  Widget buildIconWidget(
    String? iconName,
    String? iconPath, {
    double size = 24,
    Color? color,
  }) {
    // If we have a custom image path, try to display it
    if (iconPath != null && iconPath.isNotEmpty && iconPath != 'null') {
      final file = File(iconPath);
      if (file.existsSync()) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.file(
            file,
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to material icon if image fails to load
              return Icon(
                getIconData(iconName) ?? Icons.apps,
                size: size,
                color: color,
              );
            },
          ),
        );
      }
    }

    // Use material icon
    return Icon(
      getIconData(iconName) ?? Icons.apps,
      size: size,
      color: color,
    );
  }

  /// Check if an icon path represents a valid custom image
  bool isValidCustomImage(String? iconPath) {
    if (iconPath == null || iconPath.isEmpty || iconPath == 'null') {
      return false;
    }
    final file = File(iconPath);
    return file.existsSync();
  }

  /// Get a fallback icon for a given category
  IconData getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'system':
        return Icons.settings;
      case 'utilities':
        return Icons.build;
      case 'network':
        return Icons.wifi;
      case 'development':
        return Icons.code;
      case 'media':
        return Icons.play_circle;
      case 'security':
        return Icons.security;
      case 'custom':
        return Icons.extension;
      default:
        return Icons.apps;
    }
  }
}
