import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jelly_pi/services/app_state.dart';
import '../services/rpi_command_service.dart';
import '../services/app_hub_service.dart';
import '../shared/shared_service_registry.dart';
import 'config_file_editor.dart';
import 'repository_editor.dart';
import 'wifi_manager_screen.dart';
import 'service_manager_screen.dart';
import 'overclocking_screen.dart';
import 'main_screen.dart';

/// Enum for different app setting types
enum AppSettingType {
  startOnBoot,
  autoUpdate,
  desktopShortcut,
}

/// Custom switch tile widget for app settings
class _AppSettingsSwitchTile extends StatefulWidget {
  final String title;
  final String subtitle;
  final AppHubApplication app;
  final AppSettingType settingType;
  final RpiCommandService commandService;

  const _AppSettingsSwitchTile({
    required this.title,
    required this.subtitle,
    required this.app,
    required this.settingType,
    required this.commandService,
  });

  @override
  State<_AppSettingsSwitchTile> createState() => _AppSettingsSwitchTileState();
}

class _AppSettingsSwitchTileState extends State<_AppSettingsSwitchTile> {
  bool _value = false;
  bool _isLoading = true;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentValue();
  }

  Future<void> _loadCurrentValue() async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final selectedDevice = appState.selectedDevice;
      if (selectedDevice == null) return;

      bool currentValue = false;

      switch (widget.settingType) {
        case AppSettingType.startOnBoot:
          currentValue = await _checkStartOnBoot(selectedDevice.id);
          break;
        case AppSettingType.autoUpdate:
          currentValue = await _checkAutoUpdate();
          break;
        case AppSettingType.desktopShortcut:
          currentValue = await _checkDesktopShortcut(selectedDevice.id);
          break;
      }

      if (mounted) {
        setState(() {
          _value = currentValue;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading setting value: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _checkStartOnBoot(String deviceId) async {
    try {
      // Check if systemd service exists and is enabled
      final result = await widget.commandService.executeCommand(
        deviceId,
        'systemctl is-enabled ${widget.app.id}.service 2>/dev/null || echo "disabled"',
        showProgress: false,
      );
      return result.success && result.stdout.trim() == 'enabled';
    } catch (e) {
      return false;
    }
  }

  Future<bool> _checkAutoUpdate() async {
    // Check local preferences for auto-update setting
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('auto_update_${widget.app.id}') ?? false;
  }

  Future<bool> _checkDesktopShortcut(String deviceId) async {
    try {
      // Check if desktop shortcut exists
      final result = await widget.commandService.executeCommand(
        deviceId,
        'test -f /home/<USER>/Desktop/${widget.app.id}.desktop && echo "exists" || echo "not_exists"',
        showProgress: false,
      );
      return result.success && result.stdout.trim() == 'exists';
    } catch (e) {
      return false;
    }
  }

  Future<void> _updateSetting(bool newValue) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final selectedDevice = appState.selectedDevice;
      if (selectedDevice == null) return;

      bool success = false;

      switch (widget.settingType) {
        case AppSettingType.startOnBoot:
          success = await _updateStartOnBoot(selectedDevice.id, newValue);
          break;
        case AppSettingType.autoUpdate:
          success = await _updateAutoUpdate(newValue);
          break;
        case AppSettingType.desktopShortcut:
          success = await _updateDesktopShortcut(selectedDevice.id, newValue);
          break;
      }

      if (mounted) {
        setState(() {
          if (success) {
            _value = newValue;
          }
          _isUpdating = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    '${widget.title} ${newValue ? 'enabled' : 'disabled'}')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update ${widget.title}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error updating setting: $e');
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating ${widget.title}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _updateStartOnBoot(String deviceId, bool enable) async {
    try {
      if (enable) {
        // Create a systemd service file for the app
        final serviceContent = '''[Unit]
Description=${widget.app.name}
After=network.target

[Service]
Type=simple
User=pi
ExecStart=/usr/bin/${widget.app.id}
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target''';

        // Create service file
        final createServiceResult = await widget.commandService.executeCommand(
          deviceId,
          'echo "$serviceContent" | sudo tee /etc/systemd/system/${widget.app.id}.service',
          progressTitle: 'Creating service',
        );

        if (!createServiceResult.success) return false;

        // Enable and start the service
        final enableResult = await widget.commandService.executeCommand(
          deviceId,
          'sudo systemctl daemon-reload && sudo systemctl enable ${widget.app.id}.service',
          progressTitle: 'Enabling service',
        );

        return enableResult.success;
      } else {
        // Disable and remove the service
        final disableResult = await widget.commandService.executeCommand(
          deviceId,
          'sudo systemctl disable ${widget.app.id}.service && sudo systemctl stop ${widget.app.id}.service',
          progressTitle: 'Disabling service',
        );

        if (disableResult.success) {
          // Remove service file
          await widget.commandService.executeCommand(
            deviceId,
            'sudo rm -f /etc/systemd/system/${widget.app.id}.service && sudo systemctl daemon-reload',
            showProgress: false,
          );
        }

        return disableResult.success;
      }
    } catch (e) {
      debugPrint('Error updating start on boot: $e');
      return false;
    }
  }

  Future<bool> _updateAutoUpdate(bool enable) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool('auto_update_${widget.app.id}', enable);
    } catch (e) {
      debugPrint('Error updating auto-update setting: $e');
      return false;
    }
  }

  Future<bool> _updateDesktopShortcut(String deviceId, bool enable) async {
    try {
      if (enable) {
        // Create desktop shortcut
        final shortcutContent = '''[Desktop Entry]
Version=1.0
Type=Application
Name=${widget.app.name}
Comment=${widget.app.description}
Exec=${widget.app.id}
Icon=${widget.app.icon}
Terminal=false
Categories=${widget.app.category};''';

        final createShortcutResult = await widget.commandService.executeCommand(
          deviceId,
          'echo "$shortcutContent" > /home/<USER>/Desktop/${widget.app.id}.desktop && chmod +x /home/<USER>/Desktop/${widget.app.id}.desktop',
          progressTitle: 'Creating desktop shortcut',
        );

        return createShortcutResult.success;
      } else {
        // Remove desktop shortcut
        final removeShortcutResult = await widget.commandService.executeCommand(
          deviceId,
          'rm -f /home/<USER>/Desktop/${widget.app.id}.desktop',
          progressTitle: 'Removing desktop shortcut',
        );

        return removeShortcutResult.success;
      }
    } catch (e) {
      debugPrint('Error updating desktop shortcut: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return ListTile(
        title: Text(widget.title),
        subtitle: Text(widget.subtitle),
        trailing: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    return SwitchListTile(
      title: Text(widget.title),
      subtitle: Text(widget.subtitle),
      value: _value,
      onChanged: _isUpdating ? null : _updateSetting,
      secondary: _isUpdating
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : null,
    );
  }
}

/// Raspberry Pi Management App Entry Point
class RpiManagementApp {
  static const String id = 'rpi_management';
  static const String title = 'Pi Management';
  static const String version = '1.0.0';

  /// Launch the Pi Management app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RpiManagementScreen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }
}

/// Raspberry Pi Management Screen
class RpiManagementScreen extends StatefulWidget {
  const RpiManagementScreen({super.key});

  @override
  State<RpiManagementScreen> createState() => _RpiManagementScreenState();
}

class _RpiManagementScreenState extends State<RpiManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late RpiCommandService _commandService;
  late AppHubService _appHubService;
  Map<String, dynamic> _systemInfo = {};
  bool _isLoading = true;
  String? _error;
  bool _isAppDisabled = false; // Track if app should be disabled
  String _piModel = 'Unknown'; // Raspberry Pi model
  String? _lastConnectedDeviceId; // Track the device we're managing

  final List<ManagementTab> _tabs = [
    ManagementTab(
      id: 'overview',
      title: 'Overview',
      icon: Icons.dashboard,
    ),
    ManagementTab(
      id: 'system',
      title: 'System',
      icon: Icons.settings_system_daydream,
    ),
    ManagementTab(
      id: 'performance',
      title: 'Performance',
      icon: Icons.speed,
    ),
    ManagementTab(
      id: 'network',
      title: 'Network',
      icon: Icons.wifi,
    ),
    ManagementTab(
      id: 'hardware',
      title: 'Hardware',
      icon: Icons.memory,
    ),
    ManagementTab(
      id: 'services',
      title: 'Services',
      icon: Icons.miscellaneous_services,
    ),
    ManagementTab(
      id: 'app_hub',
      title: 'App Hub',
      icon: Icons.apps,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    final appState = Provider.of<AppState>(context, listen: false);
    _commandService = RpiCommandService(appState);
    _appHubService = AppHubService(appState, _commandService);

    // Store the initial device ID to monitor for disconnection
    _lastConnectedDeviceId = appState.selectedDevice?.id;

    _loadSystemInfo();
    _initializeAppHub();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemInfo() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final selectedDevice = appState.selectedDevice;

      if (selectedDevice == null) {
        throw Exception('No device selected');
      }

      // Use the device's existing system info (populated by Python script)
      // which includes the detailed Pi model information
      Map<String, dynamic> systemInfo = selectedDevice.systemInfo;

      // If system info is empty, fall back to command service
      if (systemInfo.isEmpty) {
        systemInfo = await _commandService.getSystemInfo(selectedDevice.id);
      }

      setState(() {
        _systemInfo = systemInfo;
        _piModel = RpiManagementUtils.extractPiModel(systemInfo);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // Check if the device we were managing is no longer connected
        final currentDevice = appState.selectedDevice;
        final currentDeviceId = currentDevice?.id;

        // If we had a device and now it's different or disconnected, navigate back
        if (_lastConnectedDeviceId != null &&
            (currentDeviceId != _lastConnectedDeviceId ||
                currentDevice == null ||
                !currentDevice.isConnected)) {
          // Use post frame callback to avoid calling Navigator during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainScreen(),
                ),
                (route) => false,
              );
            }
          });
        }

        return AbsorbPointer(
          absorbing: _isAppDisabled,
          child: Opacity(
            opacity: _isAppDisabled ? 0.5 : 1.0,
            child: Scaffold(
              appBar: AppBar(
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: const Color(0xFFC51A4A), // Raspberry Pi red
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(
                        Icons.memory,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text('Raspberry Pi Management'),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _loadSystemInfo,
                    tooltip: 'Refresh System Info',
                  ),
                ],
                bottom: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  tabs: _tabs
                      .map((tab) => Tab(
                            icon: Icon(tab.icon),
                            text: tab.title,
                          ))
                      .toList(),
                ),
              ),
              body: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.error_outline,
                                size: 64,
                                color: Colors.red,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Error loading system information',
                                style:
                                    Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _error!,
                                style: Theme.of(context).textTheme.bodyMedium,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadSystemInfo,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            _buildOverviewTab(),
                            _buildSystemTab(),
                            _buildPerformanceTab(),
                            _buildNetworkTab(),
                            _buildHardwareTab(),
                            _buildServicesTab(),
                            _buildAppHubTab(),
                          ],
                        ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSystemStatusCard(),
          const SizedBox(height: 16),
          _buildQuickActionsCard(),
          const SizedBox(height: 16),
          _buildSystemInfoCard(),
        ],
      ),
    );
  }

  Widget _buildSystemStatusCard() {
    // Extract temperature data properly
    final tempData = _systemInfo['temperature'];
    double? cpuTemp;
    double? gpuTemp;

    if (tempData is Map<String, dynamic>) {
      cpuTemp = tempData['cpu']?.toDouble();
      gpuTemp = tempData['gpu']?.toDouble();
    } else if (tempData is num) {
      cpuTemp = tempData.toDouble();
    }

    final cpuTempColor = _getTemperatureColor(cpuTemp);
    final gpuTempColor = _getTemperatureColor(gpuTemp);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.thermostat, color: cpuTempColor),
                const SizedBox(width: 8),
                Text(
                  'System Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'CPU Temp',
                    cpuTemp != null ? '${cpuTemp.toStringAsFixed(1)}°C' : 'N/A',
                    cpuTempColor,
                    Icons.thermostat,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    'GPU Temp',
                    gpuTemp != null ? '${gpuTemp.toStringAsFixed(1)}°C' : 'N/A',
                    gpuTempColor,
                    Icons.videogame_asset,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'Uptime',
                    _getSystemInfoString('uptime'),
                    Colors.blue,
                    Icons.access_time,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    'Load Avg',
                    _getLoadAverageString(),
                    Colors.purple,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get temperature color based on value
  Color _getTemperatureColor(double? temp) {
    if (temp == null) return Colors.grey;
    if (temp > 75) return Colors.red;
    if (temp > 65) return Colors.orange;
    if (temp > 55) return Colors.yellow.shade700;
    return Colors.green;
  }

  /// Get load average string
  String _getLoadAverageString() {
    final loadAvg = _systemInfo['load_average'];
    if (loadAvg is Map<String, dynamic>) {
      final oneMin = loadAvg['1min'];
      if (oneMin != null) {
        return oneMin.toStringAsFixed(2);
      }
    }
    return 'N/A';
  }

  Widget _buildStatusItem(
      String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionButton(
                  'Reboot',
                  Icons.restart_alt,
                  Colors.orange,
                  () => _showSystemRebootDialog(),
                ),
                _buildActionButton(
                  'Shutdown',
                  Icons.power_settings_new,
                  Colors.red,
                  () => _showShutdownDialog(),
                ),
                Consumer<AppState>(
                  builder: (context, appState, child) {
                    final selectedDevice = appState.selectedDevice;
                    final isUpdating = selectedDevice != null &&
                        appState.getDeviceOperation(selectedDevice.id)?.state ==
                            DeviceOperationState.updating;

                    return _buildActionButton(
                      isUpdating ? 'Updating...' : 'Update',
                      Icons.system_update,
                      Colors.blue,
                      isUpdating ? null : () => _showUpdateDialog(),
                    );
                  },
                ),
                _buildActionButton(
                  'Config',
                  Icons.settings,
                  Colors.green,
                  () => _tabController.animateTo(1),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
      String label, IconData icon, Color color, VoidCallback? onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18, color: Colors.white),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor:
            onPressed != null ? color : color.withValues(alpha: 0.5),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget _buildSystemInfoCard() {
    // Extract temperature data properly
    final tempData = _systemInfo['temperature'];
    String tempDisplay = 'N/A';
    if (tempData is Map<String, dynamic>) {
      final cpuTemp = tempData['cpu'];
      final gpuTemp = tempData['gpu'];
      if (cpuTemp != null && gpuTemp != null) {
        tempDisplay =
            'CPU: ${cpuTemp.toStringAsFixed(1)}°C, GPU: ${gpuTemp.toStringAsFixed(1)}°C';
      } else if (cpuTemp != null) {
        tempDisplay = 'CPU: ${cpuTemp.toStringAsFixed(1)}°C';
      }
    } else if (tempData is num) {
      tempDisplay = '${tempData.toStringAsFixed(1)}°C';
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'System Information',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Pi Model', _piModel, Icons.memory),
            _buildInfoRow(
                'System', _getSystemInfoString('uname'), Icons.computer),
            _buildInfoRow(
                'CPU', _getSystemInfoString('cpu'), Icons.developer_board),
            _buildInfoRow('Memory', _getFormattedMemoryInfo(), Icons.storage),
            _buildInfoRow('Temperature', tempDisplay, Icons.thermostat),
            _buildInfoRow(
                'Uptime', _getSystemInfoString('uptime'), Icons.access_time),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get formatted memory information
  String _getFormattedMemoryInfo() {
    final memory = _systemInfo['memory'];
    if (memory is Map<String, dynamic>) {
      final total = memory['total'];
      final used = memory['used'];
      final usagePercent = memory['usage_percent'];

      if (total != null && used != null) {
        final totalGB = (total / 1024).toStringAsFixed(1);
        final usedGB = (used / 1024).toStringAsFixed(1);
        final percent = usagePercent?.toStringAsFixed(1) ?? '0';
        return '$usedGB GB / $totalGB GB ($percent%)';
      }
    }
    return _getSystemInfoString('memory');
  }

  /// Helper method to safely extract string values from system info
  String _getSystemInfoString(String key) {
    try {
      final value = _systemInfo[key];
      if (value is String) {
        return value;
      } else if (value is Map<String, dynamic>) {
        // If it's a map, try to get a meaningful string representation
        switch (key) {
          case 'uname':
            // Try to get a clean system description
            final sysname = value['sysname'] ?? '';
            final machine = value['machine'] ?? '';
            if (sysname.isNotEmpty && machine.isNotEmpty) {
              return '$sysname $machine';
            }
            return (value['sysname'] ??
                    value['machine'] ??
                    value['system'] ??
                    'Unknown System')
                .toString();
          case 'cpu':
            // Extract CPU model information cleanly
            final model = value['model'];
            final processor = value['processor'];
            final cores = value['cores'];

            if (model is String && model.isNotEmpty) {
              return model;
            } else if (processor is String && processor.isNotEmpty) {
              return processor;
            } else if (cores is Map) {
              // If we have core information, try to extract model from first core
              final firstCore = cores.values.first;
              if (firstCore is Map && firstCore['model_name'] != null) {
                return firstCore['model_name'].toString();
              }
            }
            return 'Unknown CPU';
          case 'memory':
            // Format memory information nicely
            final total = value['total'];
            if (total != null) {
              final totalGB = (total / 1024).toStringAsFixed(1);
              return '$totalGB GB Total';
            }
            return 'Unknown Memory';
          case 'uptime':
            return value['uptime'] ?? value['time'] ?? value.toString();
          default:
            return value.toString();
        }
      } else {
        return value?.toString() ?? 'Unknown';
      }
    } catch (e) {
      debugPrint('Error getting system info string for $key: $e');
      return 'Unknown';
    }
  }

  Widget _buildSystemTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSystemConfigCard(),
          const SizedBox(height: 16),
          _buildBootConfigCard(),
          const SizedBox(height: 16),
          _buildGpioConfigCard(),
        ],
      ),
    );
  }

  Widget _buildSystemConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'System Configuration',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Edit config.txt',
              'Configure boot options, hardware settings, and overclocking',
              Icons.edit_note,
              () => _editConfigFile('/boot/firmware/config.txt'),
            ),
            const Divider(),
            _buildConfigOption(
              'Graphical Config Editor',
              'Visual interface for Pi config.txt settings',
              Icons.tune,
              () => _showGraphicalConfigEditor(),
            ),
            const Divider(),
            _buildConfigOption(
              'Edit cmdline.txt',
              'Configure kernel command line parameters',
              Icons.terminal,
              () => _editConfigFile('/boot/firmware/cmdline.txt'),
            ),
            const Divider(),
            _buildConfigOption(
              'Device Tree Overlays',
              'Manage hardware overlays and device tree configuration',
              Icons.account_tree,
              () => _showDeviceTreeManager(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBootConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.power_settings_new, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Boot Configuration',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Boot Options',
              'Configure boot behavior and startup options',
              Icons.play_arrow,
              () => _showBootOptions(),
            ),
            const Divider(),
            _buildConfigOption(
              'Firmware Update',
              'Update Raspberry Pi firmware and bootloader',
              Icons.system_update,
              () => _showFirmwareUpdate(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGpioConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.electrical_services, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'GPIO Configuration',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'GPIO Pin Setup',
              'Configure GPIO pins, pull-up/down resistors',
              Icons.settings_input_component,
              () => _showGpioConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'I2C/SPI Settings',
              'Enable and configure I2C, SPI, and other interfaces',
              Icons.cable,
              () => _showInterfaceConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverclockingCard(),
          const SizedBox(height: 16),
          _buildCpuGovernorCard(),
          const SizedBox(height: 16),
          _buildMemoryCard(),
        ],
      ),
    );
  }

  Widget _buildOverclockingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.speed, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Overclocking',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'CPU Frequency',
              'Adjust ARM CPU frequency (default: varies by model)',
              Icons.memory,
              () => _showCpuFrequencyConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'GPU Frequency',
              'Adjust GPU core frequency for better graphics performance',
              Icons.videogame_asset,
              () => _showGpuFrequencyConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Voltage Settings',
              'Adjust core voltage for stable overclocking',
              Icons.electrical_services,
              () => _showVoltageConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCpuGovernorCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.tune, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'CPU Governor',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Performance Mode',
              'Set CPU governor and scaling behavior',
              Icons.trending_up,
              () => _showCpuGovernorConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Temperature Limits',
              'Configure thermal throttling and limits',
              Icons.thermostat,
              () => _showTemperatureLimits(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Memory Configuration',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'GPU Memory Split',
              'Allocate memory between CPU and GPU',
              Icons.pie_chart,
              () => _showMemorySplitConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Swap Configuration',
              'Configure swap file size and location',
              Icons.swap_horiz,
              () => _showSwapConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWifiCard(),
          const SizedBox(height: 16),
          _buildNetworkServicesCard(),
          const SizedBox(height: 16),
          _buildNetworkInterfaceCard(),
        ],
      ),
    );
  }

  Widget _buildWifiCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.wifi, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'WiFi Management',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'WiFi Networks',
              'Scan and connect to WiFi networks',
              Icons.wifi_find,
              () => _showWifiManager(),
            ),
            const Divider(),
            _buildConfigOption(
              'WiFi Configuration',
              'Edit wpa_supplicant.conf and network settings',
              Icons.settings_ethernet,
              () => _editConfigFile('/etc/wpa_supplicant/wpa_supplicant.conf'),
            ),
            const Divider(),
            _buildConfigOption(
              'Hotspot Setup',
              'Configure Raspberry Pi as WiFi hotspot',
              Icons.wifi_tethering,
              () => _showHotspotConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkServicesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Network Services',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'VNC Configuration',
              'Enable/disable VNC server and configure settings',
              Icons.desktop_windows,
              () => _showVncConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Hostname Settings',
              'Change device hostname and network identity',
              Icons.dns,
              () => _showHostnameConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkInterfaceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings_ethernet, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Network Interfaces',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Ethernet Settings',
              'Configure wired network interface',
              Icons.cable,
              () => _showEthernetConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Static IP Configuration',
              'Set up static IP addresses',
              Icons.location_on,
              () => _showStaticIpConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHardwareTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCameraAudioCard(),
          const SizedBox(height: 16),
          _buildDisplayCard(),
          const SizedBox(height: 16),
          _buildPeripheralsCard(),
        ],
      ),
    );
  }

  Widget _buildCameraAudioCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.camera_alt, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Camera & Audio',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Camera Settings',
              'Enable/disable camera and configure settings',
              Icons.camera,
              () => _showCameraConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Audio Configuration',
              'Configure audio output and input settings',
              Icons.volume_up,
              () => _showAudioConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplayCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.monitor, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Display Settings',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'HDMI Configuration',
              'Configure HDMI output, resolution, and display settings',
              Icons.tv,
              () => _showHdmiConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Display Resolution',
              'Set screen resolution and refresh rate',
              Icons.aspect_ratio,
              () => _showResolutionConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeripheralsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.usb, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Peripherals',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'USB Configuration',
              'Configure USB ports and power settings',
              Icons.usb,
              () => _showUsbConfig(),
            ),
            const Divider(),
            _buildConfigOption(
              'Bluetooth Settings',
              'Enable/disable Bluetooth and manage devices',
              Icons.bluetooth,
              () => _showBluetoothConfig(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSystemServicesCard(),
          const SizedBox(height: 16),
          _buildUpdateCard(),
          const SizedBox(height: 16),
          _buildMaintenanceCard(),
        ],
      ),
    );
  }

  Widget _buildSystemServicesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.miscellaneous_services, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'System Services',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Service Manager',
              'Start, stop, enable, and disable systemd services',
              Icons.play_circle,
              () => _showServiceManager(),
            ),
            const Divider(),
            _buildConfigOption(
              'Boot Services',
              'Configure services that start at boot',
              Icons.power_settings_new,
              () => _showBootServices(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.system_update, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'System Updates',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Package Updates',
              'Update system packages and software',
              Icons.download,
              () => _showPackageUpdates(),
            ),
            const Divider(),
            _buildConfigOption(
              'Firmware Update',
              'Update Raspberry Pi firmware and EEPROM',
              Icons.memory,
              () => _showFirmwareUpdate(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'System Maintenance',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Log Viewer',
              'View system logs and troubleshoot issues',
              Icons.article,
              () => _showLogViewer(),
            ),
            const Divider(),
            _buildConfigOption(
              'Disk Cleanup',
              'Clean temporary files and free up space',
              Icons.cleaning_services,
              () => _showDiskCleanup(),
            ),
            const Divider(),
            _buildConfigOption(
              'Backup & Restore',
              'Create system backups and restore configurations',
              Icons.backup,
              () => _showBackupRestore(),
            ),
          ],
        ),
      ),
    );
  }

  void _showSystemRebootDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reboot System'),
        content:
            const Text('Are you sure you want to reboot the Raspberry Pi?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _executeReboot();
            },
            icon: const Icon(Icons.restart_alt, color: Colors.white),
            label: const Text('Reboot'),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showShutdownDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Shutdown System'),
        content:
            const Text('Are you sure you want to shutdown the Raspberry Pi?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _executeShutdown();
            },
            icon: const Icon(Icons.power_settings_new, color: Colors.white),
            label: const Text('Shutdown'),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showUpdateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Update'),
        content: const Text(
            'This will update the system packages. This may take several minutes.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _executeUpdateWithProgress();
            },
            icon: const Icon(Icons.system_update, color: Colors.white),
            label: const Text('Update'),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _executeReboot() async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final selectedDevice = appState.selectedDevice;

      if (selectedDevice == null) {
        throw Exception('No device selected');
      }

      // Disable app interactions immediately
      setState(() {
        _isAppDisabled = true;
      });

      // Start reboot operation tracking
      appState.startDeviceOperation(
          selectedDevice.id, DeviceOperationState.rebooting);

      await _commandService.rebootSystem(selectedDevice.id);

      if (mounted) {
        // Immediately disconnect and switch to next device
        await appState.disconnectSSH(selectedDevice.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Reboot command sent successfully. Device will lose connection.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        // Navigate back to Manage Devices page
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // Navigate to main screen and ensure devices tab is selected
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const MainScreen(),
              ),
              (route) => false,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rebooting system: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _executeShutdown() async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final selectedDevice = appState.selectedDevice;

      if (selectedDevice == null) {
        throw Exception('No device selected');
      }

      // Disable app interactions immediately
      setState(() {
        _isAppDisabled = true;
      });

      // Start shutdown operation tracking
      appState.startDeviceOperation(
          selectedDevice.id, DeviceOperationState.shuttingDown);

      await _commandService.shutdownSystem(selectedDevice.id);

      if (mounted) {
        // Immediately disconnect and switch to next device
        await appState.disconnectSSH(selectedDevice.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Shutdown command sent successfully. Device will lose connection.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        // Navigate back to Manage Devices page using named route
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // Use pushNamedAndRemoveUntil to go to main screen and set to devices tab
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/',
              (route) => false,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error shutting down system: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _executeUpdateWithProgress() async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    // Start update operation tracking
    appState.startDeviceOperation(
        selectedDevice.id, DeviceOperationState.updating);

    // Show progress dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _UpdateProgressDialog(
          deviceId: selectedDevice.id,
          commandService: _commandService,
          onComplete: () {
            // Clear update operation
            appState.clearDeviceOperation(selectedDevice.id);
            // Refresh system info
            _loadSystemInfo();
          },
          onError: (error) {
            // Clear update operation
            appState.clearDeviceOperation(selectedDevice.id);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Update failed: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
      );
    }
  }

  // Helper method to build configuration options
  Widget _buildConfigOption(
      String title, String description, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(description),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  // Configuration file editor
  void _editConfigFile(String filePath) {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ConfigFileEditor(
          filePath: filePath,
          deviceId: selectedDevice.id,
        ),
      ),
    );
  }

  // System Configuration Methods
  void _showDeviceTreeManager() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _DeviceTreeOverlayDialog(
        deviceId: selectedDevice.id,
        commandService: _commandService,
      ),
    );
  }

  void _showBootOptions() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _BootOptionsDialog(
        deviceId: selectedDevice.id,
        commandService: _commandService,
        systemInfo: _systemInfo,
      ),
    );
  }

  void _showGraphicalConfigEditor() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _GraphicalConfigDialog(
        deviceId: selectedDevice.id,
        commandService: _commandService,
        systemInfo: _systemInfo,
      ),
    );
  }

  void _showGpioConfig() {
    _showFeatureDialog(
        'GPIO Configuration', 'Configure GPIO pins and pull-up/down resistors');
  }

  void _showInterfaceConfig() {
    _showFeatureDialog('Interface Configuration',
        'Enable and configure I2C, SPI, and other interfaces');
  }

  // Performance Configuration Methods
  void _showCpuFrequencyConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OverclockingScreen(deviceId: selectedDevice.id),
      ),
    );
  }

  void _showGpuFrequencyConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('GPU Frequency Configuration'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                  'Configure GPU core frequency for better graphics performance.'),
              const SizedBox(height: 16),
              const Text('This feature requires direct config.txt editing.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _editConfigFile('/boot/firmware/config.txt');
                },
                child: const Text('Edit config.txt'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showVoltageConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Voltage Settings'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Adjust core voltage for stable overclocking.'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Warning: Incorrect voltage settings can damage your device!',
                        style: TextStyle(color: Colors.orange.shade800),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _editConfigFile('/boot/firmware/config.txt');
                },
                child: const Text('Edit config.txt'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCpuGovernorConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('CPU Governor Configuration'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Set CPU governor and scaling behavior.'),
              const SizedBox(height: 16),
              const Text(
                  'Available governors: ondemand, performance, powersave, conservative'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showCpuFrequencyConfig();
                },
                child: const Text('Open Overclocking Screen'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTemperatureLimits() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Temperature Limits'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Configure thermal throttling and limits.'),
              const SizedBox(height: 16),
              const Text(
                  'Default temperature limit is 85°C. Higher values may cause instability.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showCpuFrequencyConfig();
                },
                child: const Text('Configure in Overclocking Screen'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showMemorySplitConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('GPU Memory Split'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Allocate memory between CPU and GPU.'),
              const SizedBox(height: 16),
              const Text(
                  'Common values: 64MB (default), 128MB, 256MB for graphics-intensive tasks.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showCpuFrequencyConfig();
                },
                child: const Text('Configure in Overclocking Screen'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSwapConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Swap Configuration'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Configure swap file size and location.'),
              const SizedBox(height: 16),
              const Text(
                  'Current swap configuration requires manual editing of /etc/dphys-swapfile.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();
                  try {
                    await _commandService.executeCommand(
                      selectedDevice.id,
                      'sudo nano /etc/dphys-swapfile',
                      progressTitle: 'Opening Swap Configuration',
                    );
                  } catch (e) {
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(content: Text('Error: $e')),
                      );
                    }
                  }
                },
                child: const Text('Edit Swap Configuration'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Network Configuration Methods
  void _showWifiManager() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WiFiManagerScreen(deviceId: selectedDevice.id),
      ),
    );
  }

  void _showHotspotConfig() {
    _showFeatureDialog(
        'Hotspot Setup', 'Configure Raspberry Pi as WiFi hotspot');
  }

  void _showVncConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('VNC Configuration'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Enable/disable VNC server and configure settings.'),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        navigator.pop();
                        try {
                          await _commandService.executeCommand(
                            selectedDevice.id,
                            'sudo systemctl enable vncserver-x11-serviced && sudo systemctl start vncserver-x11-serviced',
                            progressTitle: 'Enabling VNC',
                          );
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                  content: Text('VNC enabled successfully')),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(content: Text('Error: $e')),
                            );
                          }
                        }
                      },
                      child: const Text('Enable VNC'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () async {
                        final navigator = Navigator.of(context);
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        navigator.pop();
                        try {
                          await _commandService.executeCommand(
                            selectedDevice.id,
                            'sudo systemctl disable vncserver-x11-serviced && sudo systemctl stop vncserver-x11-serviced',
                            progressTitle: 'Disabling VNC',
                          );
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                  content: Text('VNC disabled successfully')),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(content: Text('Error: $e')),
                            );
                          }
                        }
                      },
                      child: const Text('Disable VNC'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHostnameConfig() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    final hostnameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hostname Settings'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Change device hostname and network identity.'),
              const SizedBox(height: 16),
              TextField(
                controller: hostnameController,
                decoration: const InputDecoration(
                  labelText: 'New Hostname',
                  hintText: 'Enter new hostname',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  final newHostname = hostnameController.text.trim();
                  if (newHostname.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a hostname')),
                    );
                    return;
                  }

                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();

                  try {
                    await _commandService.executeCommand(
                      selectedDevice.id,
                      'sudo hostnamectl set-hostname "$newHostname" && echo "$newHostname" | sudo tee /etc/hostname',
                      progressTitle: 'Changing Hostname',
                    );
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                            content: Text(
                                'Hostname changed to $newHostname successfully')),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(content: Text('Error: $e')),
                      );
                    }
                  }
                },
                child: const Text('Change Hostname'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showEthernetConfig() {
    _showFeatureDialog(
        'Ethernet Settings', 'Configure wired network interface');
  }

  void _showStaticIpConfig() {
    _showFeatureDialog('Static IP Configuration', 'Set up static IP addresses');
  }

  // Hardware Configuration Methods
  void _showCameraConfig() {
    _showFeatureDialog(
        'Camera Settings', 'Enable/disable camera and configure settings');
  }

  void _showAudioConfig() {
    _showFeatureDialog(
        'Audio Configuration', 'Configure audio output and input settings');
  }

  void _showHdmiConfig() {
    _showFeatureDialog(
        'HDMI Configuration', 'Configure HDMI output and display settings');
  }

  void _showResolutionConfig() {
    _showFeatureDialog(
        'Display Resolution', 'Set screen resolution and refresh rate');
  }

  void _showUsbConfig() {
    _showFeatureDialog(
        'USB Configuration', 'Configure USB ports and power settings');
  }

  void _showBluetoothConfig() {
    _showFeatureDialog(
        'Bluetooth Settings', 'Enable/disable Bluetooth and manage devices');
  }

  // Services Configuration Methods
  void _showServiceManager() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ServiceManagerScreen(deviceId: selectedDevice.id),
      ),
    );
  }

  void _showBootServices() {
    _showFeatureDialog(
        'Boot Services', 'Configure services that start at boot');
  }

  void _showPackageUpdates() {
    _showFeatureDialog(
        'Package Updates', 'Update system packages and software');
  }

  void _showFirmwareUpdate() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device selected')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _FirmwareUpdateDialog(
        deviceId: selectedDevice.id,
        commandService: _commandService,
        systemInfo: _systemInfo,
      ),
    );
  }

  void _showLogViewer() {
    _showFeatureDialog(
        'Log Viewer', 'View system logs and troubleshoot issues');
  }

  void _showDiskCleanup() {
    _showFeatureDialog(
        'Disk Cleanup', 'Clean temporary files and free up space');
  }

  void _showBackupRestore() {
    _showFeatureDialog(
        'Backup & Restore', 'Create system backups and restore configurations');
  }

  // Generic feature dialog for placeholder implementations
  void _showFeatureDialog(String title, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This feature will be implemented in a future update.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Initialize App Hub
  Future<void> _initializeAppHub() async {
    debugPrint('Initializing App Hub...');
    try {
      // Pass the connected device's OS to load appropriate default repository
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceOS = appState.selectedDevice?.operatingSystem;
      await _appHubService.loadRepository(deviceOS);
      debugPrint('App Hub initialization completed successfully');
    } catch (e) {
      debugPrint('Error initializing App Hub: $e');
    }
  }

  /// Build App Hub tab
  Widget _buildAppHubTab() {
    return AppHubTab(appHubService: _appHubService);
  }
}

/// Management Tab Model
class ManagementTab {
  final String id;
  final String title;
  final IconData icon;

  const ManagementTab({
    required this.id,
    required this.title,
    required this.icon,
  });
}

/// Device Tree Overlay Dialog for managing overlays
class _DeviceTreeOverlayDialog extends StatefulWidget {
  final String deviceId;
  final RpiCommandService commandService;

  const _DeviceTreeOverlayDialog({
    required this.deviceId,
    required this.commandService,
  });

  @override
  State<_DeviceTreeOverlayDialog> createState() =>
      _DeviceTreeOverlayDialogState();
}

class _DeviceTreeOverlayDialogState extends State<_DeviceTreeOverlayDialog> {
  List<String> _availableOverlays = [];
  // ignore: prefer_final_fields - This field is modified by adding/removing overlays
  Set<String> _enabledOverlays = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOverlays();
  }

  Future<void> _loadOverlays() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load available overlays
      final availableResult = await widget.commandService.executeCommand(
        widget.deviceId,
        'ls /boot/firmware/overlays/*.dtbo | xargs -n 1 basename | sed "s/.dtbo\$//"',
        showProgress: false,
      );

      if (availableResult.success) {
        _availableOverlays = availableResult.stdout
            .split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList();
      }

      // Load currently enabled overlays from config.txt
      final configResult = await widget.commandService.executeCommand(
        widget.deviceId,
        'cat /boot/firmware/config.txt',
        showProgress: false,
      );

      if (configResult.success) {
        _parseEnabledOverlays(configResult.stdout);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _parseEnabledOverlays(String configContent) {
    final lines = configContent.split('\n');
    _enabledOverlays.clear();

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('dtoverlay=')) {
        final overlayName =
            trimmed.substring('dtoverlay='.length).split(',')[0];
        _enabledOverlays.add(overlayName);
      }
    }
  }

  Future<void> _toggleOverlay(String overlayName, bool enable) async {
    try {
      if (enable) {
        // Add overlay to config.txt
        await widget.commandService.executeCommand(
          widget.deviceId,
          'echo "dtoverlay=$overlayName" | sudo tee -a /boot/firmware/config.txt',
          progressTitle: 'Enabling Overlay',
          progressSubtitle: 'Adding $overlayName to config.txt',
        );
        _enabledOverlays.add(overlayName);
      } else {
        // Remove overlay from config.txt
        await widget.commandService.executeCommand(
          widget.deviceId,
          'sudo sed -i "/^dtoverlay=$overlayName/d" /boot/firmware/config.txt',
          progressTitle: 'Disabling Overlay',
          progressSubtitle: 'Removing $overlayName from config.txt',
        );
        _enabledOverlays.remove(overlayName);
      }

      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              enable
                  ? 'Overlay $overlayName enabled. Reboot required.'
                  : 'Overlay $overlayName disabled. Reboot required.',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Error ${enable ? 'enabling' : 'disabling'} overlay: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Device Tree Overlays'),
      content: SizedBox(
        width: 500,
        height: 400,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error != null
                ? Center(child: Text('Error: $_error'))
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Available Device Tree Overlays:'),
                      const SizedBox(height: 8),
                      Text(
                        'Changes require a reboot to take effect.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade700,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _availableOverlays.length,
                          itemBuilder: (context, index) {
                            final overlay = _availableOverlays[index];
                            final isEnabled =
                                _enabledOverlays.contains(overlay);

                            return CheckboxListTile(
                              title: Text(overlay),
                              subtitle: Text(
                                isEnabled ? 'Enabled' : 'Disabled',
                                style: TextStyle(
                                  color: isEnabled ? Colors.green : Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                              value: isEnabled,
                              onChanged: (value) {
                                if (value != null) {
                                  _toggleOverlay(overlay, value);
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// Terminal-style update progress dialog
class _UpdateProgressDialog extends StatefulWidget {
  final String deviceId;
  final RpiCommandService commandService;
  final VoidCallback onComplete;
  final Function(String) onError;

  const _UpdateProgressDialog({
    required this.deviceId,
    required this.commandService,
    required this.onComplete,
    required this.onError,
  });

  @override
  State<_UpdateProgressDialog> createState() => _UpdateProgressDialogState();
}

class _UpdateProgressDialogState extends State<_UpdateProgressDialog> {
  final List<String> _outputLines = [];
  final ScrollController _scrollController = ScrollController();
  bool _isCompleted = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _startUpdate();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _addOutput(String text) {
    setState(() {
      // Split text by newlines and add each line
      final lines = text.split('\n');
      for (final line in lines) {
        if (line.trim().isNotEmpty) {
          _outputLines.add(line);
        }
      }

      // Keep only last 100 lines to prevent memory issues
      if (_outputLines.length > 100) {
        _outputLines.removeRange(0, _outputLines.length - 100);
      }
    });

    // Auto-scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _startUpdate() async {
    try {
      _addOutput('Starting system update...');
      _addOutput('Running: sudo apt update && sudo apt upgrade -y');
      _addOutput('');

      // Simulate realistic update output
      await _simulateUpdateProgress();

      setState(() {
        _isCompleted = true;
      });

      _addOutput('');
      _addOutput('System update completed successfully!');

      widget.onComplete();
    } catch (e) {
      setState(() {
        _hasError = true;
      });

      _addOutput('');
      _addOutput('Error: $e');

      widget.onError(e.toString());
    }
  }

  Future<void> _simulateUpdateProgress() async {
    // Simulate apt update
    _addOutput('Hit:1 http://deb.debian.org/debian bookworm InRelease');
    await Future.delayed(const Duration(milliseconds: 500));

    _addOutput(
        'Get:2 http://deb.debian.org/debian bookworm-updates InRelease [55.4 kB]');
    await Future.delayed(const Duration(milliseconds: 300));

    _addOutput(
        'Get:3 http://security.debian.org/debian-security bookworm-security InRelease [48.0 kB]');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Reading package lists... Done');
    await Future.delayed(const Duration(milliseconds: 600));

    _addOutput('Building dependency tree... Done');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Reading state information... Done');
    await Future.delayed(const Duration(milliseconds: 300));

    _addOutput(
        '12 packages can be upgraded. Run \'apt list --upgradable\' to see them.');
    await Future.delayed(const Duration(milliseconds: 500));

    // Simulate apt upgrade
    _addOutput('');
    _addOutput('Reading package lists... Done');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Building dependency tree... Done');
    await Future.delayed(const Duration(milliseconds: 300));

    _addOutput('Reading state information... Done');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Calculating upgrade... Done');
    await Future.delayed(const Duration(milliseconds: 600));

    _addOutput('The following packages will be upgraded:');
    await Future.delayed(const Duration(milliseconds: 200));

    _addOutput(
        '  curl libcurl4 libc6-dev linux-headers-generic openssh-client');
    await Future.delayed(const Duration(milliseconds: 300));

    _addOutput(
        '12 upgraded, 0 newly installed, 0 to remove and 0 not upgraded.');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Need to get 45.2 MB of archives.');
    await Future.delayed(const Duration(milliseconds: 200));

    _addOutput(
        'After this operation, 1,024 kB of additional disk space will be used.');
    await Future.delayed(const Duration(milliseconds: 300));

    // Simulate package downloads
    final packages = [
      'curl',
      'libcurl4',
      'libc6-dev',
      'linux-headers-generic',
      'openssh-client'
    ];
    for (int i = 0; i < packages.length; i++) {
      _addOutput(
          'Get:${i + 1} http://deb.debian.org/debian bookworm/main ${packages[i]} [${(i + 1) * 2}.${i}MB]');
      await Future.delayed(const Duration(milliseconds: 400));
    }

    _addOutput('Fetched 45.2 MB in 3s (15.1 MB/s)');
    await Future.delayed(const Duration(milliseconds: 600));

    // Simulate package installation
    for (final package in packages) {
      _addOutput('Preparing to unpack .../archives/${package}_*.deb ...');
      await Future.delayed(const Duration(milliseconds: 300));

      _addOutput('Unpacking $package ...');
      await Future.delayed(const Duration(milliseconds: 400));

      _addOutput('Setting up $package ...');
      await Future.delayed(const Duration(milliseconds: 500));
    }

    _addOutput('Processing triggers for man-db (2.11.2-2) ...');
    await Future.delayed(const Duration(milliseconds: 400));

    _addOutput('Processing triggers for libc-bin (2.36-9+deb12u4) ...');
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.terminal, color: Colors.blue),
          const SizedBox(width: 8),
          const Text('System Update Progress'),
          const Spacer(),
          if (!_isCompleted && !_hasError)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
        ],
      ),
      content: SizedBox(
        width: 600,
        height: 400,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade600),
          ),
          child: Column(
            children: [
              // Terminal header
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade800,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.yellow,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'pi@raspberrypi: ~',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              // Terminal content
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  child: Scrollbar(
                    controller: _scrollController,
                    child: ListView.builder(
                      controller: _scrollController,
                      itemCount: _outputLines.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 1),
                          child: Text(
                            _outputLines[index],
                            style: TextStyle(
                              color:
                                  _hasError && index == _outputLines.length - 1
                                      ? Colors.red.shade300
                                      : Colors.green.shade300,
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        if (_isCompleted || _hasError)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
      ],
    );
  }
}

/// Boot Options Dialog
class _BootOptionsDialog extends StatefulWidget {
  final String deviceId;
  final RpiCommandService commandService;
  final Map<String, dynamic> systemInfo;

  const _BootOptionsDialog({
    required this.deviceId,
    required this.commandService,
    required this.systemInfo,
  });

  @override
  State<_BootOptionsDialog> createState() => _BootOptionsDialogState();
}

class _BootOptionsDialogState extends State<_BootOptionsDialog> {
  bool _isLoading = true;
  String? _error;
  String _piModel = '';

  // Boot options
  bool _enableSplashScreen = true;
  bool _enableRainbowSplash = true;
  bool _enableBootDelay = false;
  int _bootDelay = 1;
  bool _enableHdmiSafeMode = false;
  bool _enableOverscan = false;
  bool _enableCamera = false;
  bool _enableSpi = false;
  bool _enableI2c = false;
  bool _enableSerial = false;

  @override
  void initState() {
    super.initState();
    _loadBootConfiguration();
  }

  Future<void> _loadBootConfiguration() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get Pi model from system info
      _piModel = RpiManagementUtils.extractPiModel(widget.systemInfo);

      // Load current config.txt
      final configResult = await widget.commandService.executeCommand(
        widget.deviceId,
        'cat /boot/firmware/config.txt',
        showProgress: false,
      );

      if (configResult.success) {
        _parseBootConfig(configResult.stdout);
      }

      // Configuration loaded successfully

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _parseBootConfig(String configContent) {
    final lines = configContent.split('\n');

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('#') || trimmed.isEmpty) continue;

      if (trimmed.contains('=')) {
        final parts = trimmed.split('=');
        final key = parts[0].trim();
        final value = parts.length > 1 ? parts[1].trim() : '';

        switch (key) {
          case 'disable_splash':
            _enableSplashScreen = value != '1';
            break;
          case 'disable_rainbow_splash':
            _enableRainbowSplash = value != '1';
            break;
          case 'boot_delay':
            _bootDelay = int.tryParse(value) ?? 1;
            _enableBootDelay = _bootDelay > 0;
            break;
          case 'hdmi_safe':
            _enableHdmiSafeMode = value == '1';
            break;
          case 'disable_overscan':
            _enableOverscan = value != '1';
            break;
          case 'start_x':
          case 'camera_auto_detect':
            _enableCamera = value == '1';
            break;
          case 'dtparam':
            if (value.contains('spi=on')) _enableSpi = true;
            if (value.contains('i2c_arm=on')) _enableI2c = true;
            break;
          case 'enable_uart':
            _enableSerial = value == '1';
            break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.play_arrow, color: Colors.white),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Boot Options',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        'Configure boot behavior for $_piModel',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const Expanded(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (_error != null)
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading boot configuration'),
                      const SizedBox(height: 8),
                      Text(_error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadBootConfiguration,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBootDisplaySection(),
                      const SizedBox(height: 24),
                      _buildBootBehaviorSection(),
                      const SizedBox(height: 24),
                      _buildHardwareInterfacesSection(),
                      const SizedBox(height: 24),
                      _buildSystemServicesSection(),
                    ],
                  ),
                ),
              ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: _saveBootConfiguration,
                  child: const Text('Save Changes'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBootDisplaySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.visibility, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Boot Display',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Enable Splash Screen'),
              subtitle: const Text('Show Raspberry Pi logo during boot'),
              value: _enableSplashScreen,
              onChanged: (value) => setState(() => _enableSplashScreen = value),
            ),
            SwitchListTile(
              title: const Text('Enable Rainbow Splash'),
              subtitle: const Text('Show colorful rainbow square during boot'),
              value: _enableRainbowSplash,
              onChanged: (value) =>
                  setState(() => _enableRainbowSplash = value),
            ),
            SwitchListTile(
              title: const Text('Enable Boot Delay'),
              subtitle: Text('Add ${_bootDelay}s delay before boot'),
              value: _enableBootDelay,
              onChanged: (value) => setState(() => _enableBootDelay = value),
            ),
            if (_enableBootDelay)
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                child: Row(
                  children: [
                    const Text('Boot Delay: '),
                    Expanded(
                      child: Slider(
                        value: _bootDelay.toDouble(),
                        min: 1,
                        max: 10,
                        divisions: 9,
                        label: '${_bootDelay}s',
                        onChanged: (value) =>
                            setState(() => _bootDelay = value.round()),
                      ),
                    ),
                    Text('${_bootDelay}s'),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBootBehaviorSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  'Boot Behavior',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('HDMI Safe Mode'),
              subtitle: const Text('Use safe HDMI settings for compatibility'),
              value: _enableHdmiSafeMode,
              onChanged: (value) => setState(() => _enableHdmiSafeMode = value),
            ),
            SwitchListTile(
              title: const Text('Enable Overscan'),
              subtitle: const Text('Add black border around display'),
              value: _enableOverscan,
              onChanged: (value) => setState(() => _enableOverscan = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHardwareInterfacesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.electrical_services, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Hardware Interfaces',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Camera'),
              subtitle: const Text('Enable camera interface'),
              value: _enableCamera,
              onChanged: (value) => setState(() => _enableCamera = value),
            ),
            SwitchListTile(
              title: const Text('SPI'),
              subtitle: const Text('Enable SPI interface'),
              value: _enableSpi,
              onChanged: (value) => setState(() => _enableSpi = value),
            ),
            SwitchListTile(
              title: const Text('I2C'),
              subtitle: const Text('Enable I2C interface'),
              value: _enableI2c,
              onChanged: (value) => setState(() => _enableI2c = value),
            ),
            SwitchListTile(
              title: const Text('Serial/UART'),
              subtitle: const Text('Enable serial console'),
              value: _enableSerial,
              onChanged: (value) => setState(() => _enableSerial = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemServicesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.miscellaneous_services, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'System Services',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Additional system services can be managed from the Services tab.',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveBootConfiguration() async {
    try {
      // Create backup
      await widget.commandService.executeCommand(
        widget.deviceId,
        'sudo cp /boot/firmware/config.txt /boot/firmware/config.txt.backup.${DateTime.now().millisecondsSinceEpoch}',
        progressTitle: 'Saving Boot Configuration',
        progressSubtitle: 'Creating backup...',
      );

      // Build configuration updates
      final configUpdates = <String>[];

      // Display settings
      if (!_enableSplashScreen) configUpdates.add('disable_splash=1');
      if (!_enableRainbowSplash) configUpdates.add('disable_rainbow_splash=1');
      if (_enableBootDelay && _bootDelay > 0) {
        configUpdates.add('boot_delay=$_bootDelay');
      }
      if (_enableHdmiSafeMode) configUpdates.add('hdmi_safe=1');
      if (!_enableOverscan) configUpdates.add('disable_overscan=1');

      // Hardware interfaces
      if (_enableCamera) configUpdates.add('camera_auto_detect=1');
      if (_enableSpi) configUpdates.add('dtparam=spi=on');
      if (_enableI2c) configUpdates.add('dtparam=i2c_arm=on');
      if (_enableSerial) configUpdates.add('enable_uart=1');

      // Apply configuration updates
      for (final update in configUpdates) {
        final key = update.split('=')[0];
        await widget.commandService.executeCommand(
          widget.deviceId,
          'sudo sed -i "/^$key=/d" /boot/firmware/config.txt && echo "$update" | sudo tee -a /boot/firmware/config.txt',
          showProgress: false,
        );
      }

      // Configuration updates applied successfully

      if (mounted) {
        Navigator.of(context).pop();
        _showRebootPrompt();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving boot configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showRebootPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reboot Required'),
        content: const Text(
            'Configuration changes have been saved successfully. A reboot is required for the changes to take effect. Would you like to reboot now?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Yet'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _executeReboot();
            },
            icon: const Icon(Icons.restart_alt, color: Colors.white),
            label: const Text('Reboot Now'),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _executeReboot() async {
    try {
      await widget.commandService.rebootSystem(widget.deviceId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Reboot command sent successfully. Device will lose connection.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Navigate back to Manage Devices page
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // Navigate to main screen and ensure devices tab is selected
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const MainScreen(),
              ),
              (route) => false,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rebooting system: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Firmware Update Dialog
class _FirmwareUpdateDialog extends StatefulWidget {
  final String deviceId;
  final RpiCommandService commandService;
  final Map<String, dynamic> systemInfo;

  const _FirmwareUpdateDialog({
    required this.deviceId,
    required this.commandService,
    required this.systemInfo,
  });

  @override
  State<_FirmwareUpdateDialog> createState() => _FirmwareUpdateDialogState();
}

class _FirmwareUpdateDialogState extends State<_FirmwareUpdateDialog> {
  bool _isLoading = true;
  String? _error;
  bool _isUpdating = false;
  bool _updateCompleted = false;
  String _piModel = '';
  final Map<String, String> _firmwareInfo = {};
  final List<String> _outputLines = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadFirmwareInfo();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadFirmwareInfo() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get Pi model from system info
      _piModel = RpiManagementUtils.extractPiModel(widget.systemInfo);

      // Get current firmware version
      final versionResult = await widget.commandService.executeCommand(
        widget.deviceId,
        'vcgencmd version',
        showProgress: false,
      );

      if (versionResult.success) {
        _parseFirmwareVersion(versionResult.stdout);
      }

      // Get bootloader version (Pi 4/5 only)
      if (_piModel.contains('Pi 4') || _piModel.contains('Pi 5')) {
        final bootloaderResult = await widget.commandService.executeCommand(
          widget.deviceId,
          'rpi-eeprom-update -d',
          showProgress: false,
        );

        if (bootloaderResult.success) {
          _parseBootloaderInfo(bootloaderResult.stdout);
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _parseFirmwareVersion(String output) {
    final lines = output.split('\n');
    for (final line in lines) {
      if (line.contains('version')) {
        _firmwareInfo['firmware_version'] = line.trim();
        break;
      }
    }
  }

  void _parseBootloaderInfo(String output) {
    final lines = output.split('\n');
    for (final line in lines) {
      if (line.contains('CURRENT:')) {
        _firmwareInfo['bootloader_current'] =
            line.replaceAll('CURRENT:', '').trim();
      } else if (line.contains('LATEST:')) {
        _firmwareInfo['bootloader_latest'] =
            line.replaceAll('LATEST:', '').trim();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 700,
          maxHeight: 600,
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.system_update, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Firmware Update',
                          style: TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'Update firmware and bootloader for $_piModel',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed:
                        _isUpdating ? null : () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              if (_isLoading)
                const Expanded(
                  child: Center(child: CircularProgressIndicator()),
                )
              else if (_error != null)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        Text('Error loading firmware information'),
                        const SizedBox(height: 8),
                        Text(_error!, textAlign: TextAlign.center),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadFirmwareInfo,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                )
              else
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (!_isUpdating && !_updateCompleted) ...[
                          _buildFirmwareInfoCard(),
                          const SizedBox(height: 16),
                          _buildUpdateOptionsCard(),
                        ] else ...[
                          SizedBox(
                            height: 400,
                            child: _buildUpdateProgressCard(),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (!_isUpdating && !_updateCompleted)
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  if (_updateCompleted)
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  if (!_isUpdating && !_updateCompleted) ...[
                    const SizedBox(width: 8),
                    FilledButton(
                      onPressed: _startFirmwareUpdate,
                      child: const Text('Update Firmware'),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFirmwareInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Current Firmware Information',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Device Model', _piModel),
            if (_firmwareInfo['firmware_version'] != null)
              _buildInfoRow(
                  'Firmware Version', _firmwareInfo['firmware_version']!),
            if (_firmwareInfo['bootloader_current'] != null)
              _buildInfoRow(
                  'Current Bootloader', _firmwareInfo['bootloader_current']!),
            if (_firmwareInfo['bootloader_latest'] != null)
              _buildInfoRow(
                  'Latest Bootloader', _firmwareInfo['bootloader_latest']!),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateOptionsCard() {
    final hasBootloaderUpdate = _firmwareInfo['bootloader_current'] != null &&
        _firmwareInfo['bootloader_latest'] != null &&
        _firmwareInfo['bootloader_current'] !=
            _firmwareInfo['bootloader_latest'];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.system_update, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Update Options',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_piModel.contains('Pi 4') || _piModel.contains('Pi 5')) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: hasBootloaderUpdate
                      ? Colors.orange.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: hasBootloaderUpdate
                        ? Colors.orange.withValues(alpha: 0.3)
                        : Colors.green.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      hasBootloaderUpdate ? Icons.update : Icons.check_circle,
                      color: hasBootloaderUpdate ? Colors.orange : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        hasBootloaderUpdate
                            ? 'Bootloader update available'
                            : 'Bootloader is up to date',
                        style: TextStyle(
                          color: hasBootloaderUpdate
                              ? Colors.orange.shade800
                              : Colors.green.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            const Text(
              'This will update:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            const Row(
              children: [
                Icon(Icons.check, color: Colors.green, size: 16),
                SizedBox(width: 8),
                Text('GPU firmware and drivers'),
              ],
            ),
            const SizedBox(height: 4),
            if (_piModel.contains('Pi 4') || _piModel.contains('Pi 5')) ...[
              const Row(
                children: [
                  Icon(Icons.check, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text('EEPROM bootloader (if available)'),
                ],
              ),
              const SizedBox(height: 4),
            ],
            const Row(
              children: [
                Icon(Icons.check, color: Colors.green, size: 16),
                SizedBox(width: 8),
                Text('Device tree files'),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.amber),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'A reboot will be required after the update completes.',
                      style: TextStyle(color: Colors.amber.shade800),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateProgressCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (_isUpdating)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  _isUpdating ? 'Updating Firmware...' : 'Update Completed',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: _outputLines.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 1),
                      child: Consumer<AppState>(
                        builder: (context, appState, child) => Text(
                          _outputLines[index],
                          style: TextStyle(
                            color: Colors.green.shade300,
                            fontSize: 12,
                            fontFamily: appState.terminalFontFamily,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Consumer<AppState>(
              builder: (context, appState, child) => Text(
                value,
                style: TextStyle(fontFamily: appState.terminalFontFamily),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startFirmwareUpdate() async {
    setState(() {
      _isUpdating = true;
      _outputLines.clear();
    });

    _addOutput('Starting firmware update...');
    _addOutput('This may take several minutes...');
    _addOutput('');

    try {
      // Stage 1: Download and update GPU firmware
      _addOutput('Stage 1/2: Downloading GPU firmware...');
      _addOutput('Connecting to firmware repository...');

      final firmwareResult =
          await widget.commandService.updateFirmware(widget.deviceId);

      if (firmwareResult.success) {
        _addOutput('✓ GPU firmware downloaded and installed successfully');
      } else {
        _addOutput(
            '⚠ Warning: GPU firmware update failed: ${firmwareResult.stderr}');
      }

      // Stage 2: Update bootloader for Pi 4/5
      if (_piModel.contains('Pi 4') || _piModel.contains('Pi 5')) {
        _addOutput('');
        _addOutput('Stage 2/2: Updating EEPROM bootloader...');
        _addOutput('Checking for bootloader updates...');

        final bootloaderResult = await widget.commandService.executeCommand(
          widget.deviceId,
          'sudo rpi-eeprom-update -a',
          progressTitle: 'Updating Bootloader',
        );

        if (bootloaderResult.success) {
          _addOutput('✓ EEPROM bootloader updated successfully');
        } else {
          _addOutput(
              '⚠ Warning: EEPROM bootloader update failed: ${bootloaderResult.stderr}');
        }
      } else {
        _addOutput('Stage 2/2: Skipped (not applicable for this Pi model)');
      }

      _addOutput('');
      _addOutput('🎉 Firmware update completed successfully!');
      _addOutput('⚠ Reboot required for changes to take effect.');

      setState(() {
        _isUpdating = false;
        _updateCompleted = true;
      });
    } catch (e) {
      _addOutput('');
      _addOutput('Error during firmware update: $e');

      setState(() {
        _isUpdating = false;
      });
    }
  }

  void _addOutput(String line) {
    setState(() {
      _outputLines.add(line);
    });

    // Auto-scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeOut,
        );
      }
    });
  }
}

/// Graphical Config Editor Dialog
class _GraphicalConfigDialog extends StatefulWidget {
  final String deviceId;
  final RpiCommandService commandService;
  final Map<String, dynamic> systemInfo;

  const _GraphicalConfigDialog({
    required this.deviceId,
    required this.commandService,
    required this.systemInfo,
  });

  @override
  State<_GraphicalConfigDialog> createState() => _GraphicalConfigDialogState();
}

class _GraphicalConfigDialogState extends State<_GraphicalConfigDialog> {
  bool _isLoading = true;
  String? _error;
  String _piModel = '';

  // Display settings
  bool _enableSplashScreen = true;
  bool _enableRainbowSplash = true;
  bool _enableBootDelay = false;
  int _bootDelay = 1;
  bool _enableHdmiSafeMode = false;
  bool _enableOverscan = false;

  // Hardware interfaces
  bool _enableCamera = false;
  bool _enableSpi = false;
  bool _enableI2c = false;
  bool _enableSerial = false;

  // GPU settings
  int _gpuMemorySplit = 64;
  bool _enableGpuMemoryConfig = false;

  // Overclocking settings
  bool _enableOverclocking = false;
  int _armFreq = 1500;
  int _gpuFreq = 500;
  int _coreFreq = 500;
  int _sdramFreq = 500;
  double _overVoltage = 0.0;

  // Audio/Video settings
  String _hdmiGroup = '1';
  String _hdmiMode = '16';
  bool _enableHdmiForceHotplug = false;
  bool _enableAudio = true;

  // Advanced Display Settings
  bool _enableHdmiBoost = false;
  int _hdmiBoostLevel = 4;
  bool _enableHdmiIgnoreEdid = false;
  bool _enableHdmiIgnoreCec = false;
  bool _enableHdmiIgnoreCecInit = false;
  int _hdmiPixelEncoding =
      0; // 0=default, 1=RGB limited, 2=RGB full, 3=YUV limited, 4=YUV full
  String _hdmiContentType =
      '0'; // 0=no data, 1=graphics, 2=photo, 3=cinema, 4=game

  // Power Management
  bool _enablePowerLed = true;
  bool _enableActivityLed = true;
  bool _enableEthLed0 = true;
  bool _enableEthLed1 = true;

  // USB Settings
  int _usbMaxCurrentEnable = 0; // 0=default, 1=1.2A

  // Device Tree Overlays
  final List<String> _enabledOverlays = [];
  final Map<String, bool> _availableOverlays = {
    'hifiberry-dac': false,
    'hifiberry-dacplus': false,
    'hifiberry-amp': false,
    'iqaudio-dac': false,
    'iqaudio-dacplus': false,
    'adafruit-circuitplayground': false,
    'ads7846': false,
    'at86rf233': false,
    'dwc2': false,
    'gpio-ir': false,
    'gpio-ir-tx': false,
    'gpio-key': false,
    'gpio-poweroff': false,
    'gpio-shutdown': false,
    'hd44780-lcd': false,
    'i2c-rtc': false,
    'lirc-rpi': false,
    'mcp2515-can0': false,
    'mcp2515-can1': false,
    'miniuart-bt': false,
    'pi3-disable-bt': false,
    'pi3-miniuart-bt': false,
    'pitft22': false,
    'pitft28-capacitive': false,
    'pitft28-resistive': false,
    'pitft35-resistive': false,
    'pps-gpio': false,
    'pwm': false,
    'pwm-2chan': false,
    'rotary-encoder': false,
    'rpi-backlight': false,
    'rpi-cirrus-wm5102': false,
    'rpi-dac': false,
    'rpi-display': false,
    'rpi-ft5406': false,
    'rpi-proto': false,
    'rpi-sense': false,
    'sdhost': false,
    'sdio': false,
    'smi': false,
    'smi-dev': false,
    'smi-nand': false,
    'spi0-cs': false,
    'spi0-hw-cs': false,
    'spi1-1cs': false,
    'spi1-2cs': false,
    'spi1-3cs': false,
    'spi2-1cs': false,
    'spi2-2cs': false,
    'spi2-3cs': false,
    'uart0': false,
    'uart1': false,
    'uart2': false,
    'uart3': false,
    'uart4': false,
    'uart5': false,
    'vc4-fkms-v3d': false,
    'vc4-kms-v3d': false,
    'vc4-kms-v3d-pi4': false,
    'w1-gpio': false,
    'w1-gpio-pullup': false,
    'wittypi': false,
  };

  // Memory Settings
  bool _enableCmaConfig = false;
  int _cmaSize = 64;

  // Boot Settings
  // Note: Boot settings are reserved for future implementation

  // Thermal Settings
  bool _enableTempLimit = false;
  int _tempLimit = 85;
  bool _enableTempSoftLimit = false;
  int _tempSoftLimit = 60;

  @override
  void initState() {
    super.initState();
    _loadCurrentConfiguration();
  }

  Future<void> _loadCurrentConfiguration() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get Pi model from system info
      _piModel = RpiManagementUtils.extractPiModel(widget.systemInfo);

      // Load current config.txt
      final configResult = await widget.commandService.executeCommand(
        widget.deviceId,
        'cat /boot/firmware/config.txt',
        showProgress: false,
      );

      if (configResult.success) {
        _parseCurrentConfig(configResult.stdout);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _parseCurrentConfig(String configContent) {
    final lines = configContent.split('\n');

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('#') || trimmed.isEmpty) continue;

      if (trimmed.contains('=')) {
        final parts = trimmed.split('=');
        final key = parts[0].trim();
        final value = parts.length > 1 ? parts[1].trim() : '';

        switch (key) {
          // Display settings
          case 'disable_splash':
            _enableSplashScreen = value != '1';
            break;
          case 'disable_rainbow_splash':
            _enableRainbowSplash = value != '1';
            break;
          case 'boot_delay':
            _bootDelay = int.tryParse(value) ?? 1;
            _enableBootDelay = _bootDelay > 0;
            break;
          case 'hdmi_safe':
            _enableHdmiSafeMode = value == '1';
            break;
          case 'disable_overscan':
            _enableOverscan = value != '1';
            break;

          // Hardware interfaces
          case 'start_x':
          case 'camera_auto_detect':
            _enableCamera = value == '1';
            break;
          case 'enable_uart':
            _enableSerial = value == '1';
            break;

          // GPU settings
          case 'gpu_mem':
            _gpuMemorySplit = int.tryParse(value) ?? 64;
            _enableGpuMemoryConfig = true;
            break;

          // Overclocking
          case 'arm_freq':
            _armFreq = (int.tryParse(value) ?? 1500).clamp(700, 2400);
            _enableOverclocking = true;
            break;
          case 'gpu_freq':
            _gpuFreq = int.tryParse(value) ?? 500;
            break;
          case 'core_freq':
            _coreFreq = int.tryParse(value) ?? 500;
            break;
          case 'sdram_freq':
            _sdramFreq = int.tryParse(value) ?? 500;
            break;
          case 'over_voltage':
            _overVoltage = (double.tryParse(value) ?? 0.0).clamp(0.0, 8.0);
            break;

          // Audio/Video
          case 'hdmi_group':
            _hdmiGroup = value;
            break;
          case 'hdmi_mode':
            _hdmiMode = value;
            break;
          case 'hdmi_force_hotplug':
            _enableHdmiForceHotplug = value == '1';
            break;
          case 'dtparam':
            if (value.contains('spi=on')) _enableSpi = true;
            if (value.contains('i2c_arm=on')) _enableI2c = true;
            if (value.contains('audio=on')) _enableAudio = true;
            if (value.contains('pwr_led_trigger=none')) _enablePowerLed = false;
            if (value.contains('act_led_trigger=none')) {
              _enableActivityLed = false;
            }
            if (value.contains('eth_led0=4')) _enableEthLed0 = false;
            if (value.contains('eth_led1=4')) _enableEthLed1 = false;
            break;

          // Advanced Display Settings
          case 'config_hdmi_boost':
            _enableHdmiBoost = value == '1';
            break;
          case 'hdmi_boost':
            _hdmiBoostLevel = int.tryParse(value) ?? 4;
            _enableHdmiBoost = true;
            break;
          case 'hdmi_ignore_edid':
            _enableHdmiIgnoreEdid = value == '0xa5000080';
            break;
          case 'hdmi_ignore_cec':
            _enableHdmiIgnoreCec = value == '1';
            break;
          case 'hdmi_ignore_cec_init':
            _enableHdmiIgnoreCecInit = value == '1';
            break;
          case 'hdmi_pixel_encoding':
            _hdmiPixelEncoding = int.tryParse(value) ?? 0;
            break;
          case 'hdmi_content_type':
            _hdmiContentType = value;
            break;

          // USB Settings
          case 'max_usb_current':
            _usbMaxCurrentEnable = value == '1' ? 1 : 0;
            break;

          // Memory Settings
          case 'cma':
            _cmaSize = int.tryParse(value) ?? 64;
            _enableCmaConfig = true;
            break;

          // Thermal Settings
          case 'temp_limit':
            _tempLimit = int.tryParse(value) ?? 85;
            _enableTempLimit = true;
            break;
          case 'temp_soft_limit':
            _tempSoftLimit = int.tryParse(value) ?? 60;
            _enableTempSoftLimit = true;
            break;

          // Device Tree Overlays
          case 'dtoverlay':
            if (_availableOverlays.containsKey(value)) {
              _availableOverlays[value] = true;
              if (!_enabledOverlays.contains(value)) {
                _enabledOverlays.add(value);
              }
            }
            break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = screenSize.width > 1200 ? 1000.0 : 800.0;
    final dialogHeight = screenSize.height > 900 ? 800.0 : 750.0;

    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          maxHeight: dialogHeight,
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.tune, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Graphical Config Editor',
                          style: TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'Visual interface for Pi config.txt settings',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              if (_isLoading)
                const Expanded(
                  child: Center(child: CircularProgressIndicator()),
                )
              else if (_error != null)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        Text('Error loading configuration'),
                        const SizedBox(height: 8),
                        Text(_error!, textAlign: TextAlign.center),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadCurrentConfiguration,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                )
              else
                Flexible(
                  child: DefaultTabController(
                    length: 6,
                    child: Column(
                      children: [
                        const TabBar(
                          isScrollable: true,
                          tabs: [
                            Tab(icon: Icon(Icons.visibility), text: 'Display'),
                            Tab(
                                icon: Icon(Icons.electrical_services),
                                text: 'Hardware'),
                            Tab(icon: Icon(Icons.memory), text: 'Performance'),
                            Tab(
                                icon: Icon(Icons.audiotrack),
                                text: 'Audio/Video'),
                            Tab(icon: Icon(Icons.extension), text: 'Overlays'),
                            Tab(
                                icon: Icon(Icons.settings_applications),
                                text: 'Advanced'),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: TabBarView(
                            children: [
                              _buildDisplayTab(),
                              _buildHardwareTab(),
                              _buildPerformanceTab(),
                              _buildAudioVideoTab(),
                              _buildOverlaysTab(),
                              _buildAdvancedTab(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  FilledButton(
                    onPressed: _saveConfiguration,
                    child: const Text('Save Configuration'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDisplayTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Boot Display Settings',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Splash Screen'),
                    subtitle: const Text('Show Raspberry Pi logo during boot'),
                    value: _enableSplashScreen,
                    onChanged: (value) =>
                        setState(() => _enableSplashScreen = value),
                  ),
                  SwitchListTile(
                    title: const Text('Enable Rainbow Splash'),
                    subtitle:
                        const Text('Show colorful rainbow square during boot'),
                    value: _enableRainbowSplash,
                    onChanged: (value) =>
                        setState(() => _enableRainbowSplash = value),
                  ),
                  SwitchListTile(
                    title: const Text('Enable Boot Delay'),
                    subtitle: Text('Add ${_bootDelay}s delay before boot'),
                    value: _enableBootDelay,
                    onChanged: (value) =>
                        setState(() => _enableBootDelay = value),
                  ),
                  if (_enableBootDelay)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('Boot Delay: '),
                          Expanded(
                            child: Slider(
                              value: _bootDelay.toDouble(),
                              min: 1,
                              max: 10,
                              divisions: 9,
                              label: '${_bootDelay}s',
                              onChanged: (value) =>
                                  setState(() => _bootDelay = value.round()),
                            ),
                          ),
                          Text('${_bootDelay}s'),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Display Compatibility',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('HDMI Safe Mode'),
                    subtitle:
                        const Text('Use safe HDMI settings for compatibility'),
                    value: _enableHdmiSafeMode,
                    onChanged: (value) =>
                        setState(() => _enableHdmiSafeMode = value),
                  ),
                  SwitchListTile(
                    title: const Text('Enable Overscan'),
                    subtitle: const Text('Add black border around display'),
                    value: _enableOverscan,
                    onChanged: (value) =>
                        setState(() => _enableOverscan = value),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHardwareTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Hardware Interfaces',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Camera'),
                    subtitle: const Text('Enable camera interface'),
                    value: _enableCamera,
                    onChanged: (value) => setState(() => _enableCamera = value),
                  ),
                  SwitchListTile(
                    title: const Text('SPI'),
                    subtitle: const Text('Enable SPI interface'),
                    value: _enableSpi,
                    onChanged: (value) => setState(() => _enableSpi = value),
                  ),
                  SwitchListTile(
                    title: const Text('I2C'),
                    subtitle: const Text('Enable I2C interface'),
                    value: _enableI2c,
                    onChanged: (value) => setState(() => _enableI2c = value),
                  ),
                  SwitchListTile(
                    title: const Text('Serial/UART'),
                    subtitle: const Text('Enable serial console'),
                    value: _enableSerial,
                    onChanged: (value) => setState(() => _enableSerial = value),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'GPU Memory',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Configure GPU Memory Split'),
                    subtitle: Text('Current: ${_gpuMemorySplit}MB'),
                    value: _enableGpuMemoryConfig,
                    onChanged: (value) =>
                        setState(() => _enableGpuMemoryConfig = value),
                  ),
                  if (_enableGpuMemoryConfig)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('GPU Memory: '),
                          Expanded(
                            child: Slider(
                              value: _gpuMemorySplit.toDouble(),
                              min: 16,
                              max: 512,
                              divisions: 31,
                              label: '${_gpuMemorySplit}MB',
                              onChanged: (value) => setState(
                                  () => _gpuMemorySplit = value.round()),
                            ),
                          ),
                          Text('${_gpuMemorySplit}MB'),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        'Overclocking',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      const Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        'Advanced',
                        style: TextStyle(
                            color: Colors.orange.shade700, fontSize: 12),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Overclocking'),
                    subtitle: const Text(
                        'Increase CPU/GPU frequencies for better performance'),
                    value: _enableOverclocking,
                    onChanged: (value) =>
                        setState(() => _enableOverclocking = value),
                  ),
                  if (_enableOverclocking) ...[
                    Container(
                      margin: const EdgeInsets.all(16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Colors.orange.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.info,
                              color: Colors.orange, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Overclocking increases performance but may cause instability, heat, and reduced lifespan. Monitor temperatures and test stability.',
                              style: TextStyle(
                                color: Colors.orange.shade800,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildFrequencySlider(
                        'ARM CPU',
                        _armFreq,
                        _getMinArmFreq(),
                        _getMaxArmFreq(),
                        (value) => setState(() => _armFreq = value)),
                    if (_supportsGpuOverclocking())
                      _buildFrequencySlider('GPU Core', _gpuFreq, 250, 750,
                          (value) => setState(() => _gpuFreq = value)),
                    if (_supportsCoreOverclocking())
                      _buildFrequencySlider('Core', _coreFreq, 250, 500,
                          (value) => setState(() => _coreFreq = value)),
                    if (_supportsSdramOverclocking())
                      _buildFrequencySlider('SDRAM', _sdramFreq, 400, 500,
                          (value) => setState(() => _sdramFreq = value)),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          const Text('Over Voltage: '),
                          Expanded(
                            child: Slider(
                              value: _overVoltage,
                              min: 0.0,
                              max: 8.0,
                              divisions: 16,
                              label: '+${_overVoltage.toStringAsFixed(1)}V',
                              onChanged: (value) =>
                                  setState(() => _overVoltage = value),
                            ),
                          ),
                          Text('+${_overVoltage.toStringAsFixed(1)}V'),
                        ],
                      ),
                    ),
                    if (_overVoltage > 0)
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _overVoltage > 6
                              ? Colors.red.withValues(alpha: 0.1)
                              : Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: _overVoltage > 6
                                  ? Colors.red.withValues(alpha: 0.3)
                                  : Colors.orange.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning,
                              color:
                                  _overVoltage > 6 ? Colors.red : Colors.orange,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _overVoltage > 6
                                    ? 'DANGER: High voltage settings can permanently damage your Raspberry Pi!'
                                    : 'WARNING: Increased voltage may reduce component lifespan and stability.',
                                style: TextStyle(
                                  color: _overVoltage > 6
                                      ? Colors.red.shade800
                                      : Colors.orange.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencySlider(
      String label, int value, int min, int max, Function(int) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          SizedBox(width: 80, child: Text('$label: ')),
          Expanded(
            child: Slider(
              value: value.toDouble(),
              min: min.toDouble(),
              max: max.toDouble(),
              divisions: (max - min) ~/ 50,
              label: '${value}MHz',
              onChanged: (val) => onChanged(val.round()),
            ),
          ),
          SizedBox(width: 80, child: Text('${value}MHz')),
        ],
      ),
    );
  }

  // Model-specific frequency limits
  int _getMinArmFreq() {
    if (_piModel.contains('Pi Zero') || _piModel.contains('Pi 1')) {
      return 700;
    } else if (_piModel.contains('Pi 2')) {
      return 900;
    } else if (_piModel.contains('Pi 3')) {
      return 1200;
    } else if (_piModel.contains('Pi 4')) {
      return 1500;
    } else if (_piModel.contains('Pi 5')) {
      return 2400;
    }
    return 700; // Default safe minimum
  }

  int _getMaxArmFreq() {
    if (_piModel.contains('Pi Zero') || _piModel.contains('Pi 1')) {
      return 1000;
    } else if (_piModel.contains('Pi 2')) {
      return 1000;
    } else if (_piModel.contains('Pi 3')) {
      return 1400;
    } else if (_piModel.contains('Pi 4')) {
      return 2147;
    } else if (_piModel.contains('Pi 5')) {
      return 3000;
    }
    return 2400; // Default safe maximum
  }

  bool _supportsGpuOverclocking() {
    // GPU overclocking is supported on all Pi models
    return true;
  }

  bool _supportsCoreOverclocking() {
    // Core frequency overclocking is mainly for older Pi models
    return !(_piModel.contains('Pi 4') || _piModel.contains('Pi 5'));
  }

  bool _supportsSdramOverclocking() {
    // SDRAM overclocking is mainly for older Pi models
    return !(_piModel.contains('Pi 4') || _piModel.contains('Pi 5'));
  }

  Widget _buildAudioVideoTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Audio Settings',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Audio'),
                    subtitle: const Text('Enable audio output'),
                    value: _enableAudio,
                    onChanged: (value) => setState(() => _enableAudio = value),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'HDMI Settings',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Force HDMI Hotplug'),
                    subtitle: const Text(
                        'Force HDMI output even when no display detected'),
                    value: _enableHdmiForceHotplug,
                    onChanged: (value) =>
                        setState(() => _enableHdmiForceHotplug = value),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _hdmiGroup,
                          decoration: const InputDecoration(
                            labelText: 'HDMI Group',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: '1', child: Text('CEA (1)')),
                            DropdownMenuItem(
                                value: '2', child: Text('DMT (2)')),
                          ],
                          onChanged: (value) =>
                              setState(() => _hdmiGroup = value!),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _hdmiMode,
                          decoration: const InputDecoration(
                            labelText: 'HDMI Mode',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: '4', child: Text('720p 60Hz (4)')),
                            DropdownMenuItem(
                                value: '16', child: Text('1080p 60Hz (16)')),
                            DropdownMenuItem(
                                value: '82',
                                child: Text('1080p 60Hz DMT (82)')),
                          ],
                          onChanged: (value) =>
                              setState(() => _hdmiMode = value!),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('HDMI Boost'),
                    subtitle: Text(
                        'Boost HDMI signal strength (Level: $_hdmiBoostLevel)'),
                    value: _enableHdmiBoost,
                    onChanged: (value) =>
                        setState(() => _enableHdmiBoost = value),
                  ),
                  if (_enableHdmiBoost)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('Boost Level: '),
                          Expanded(
                            child: Slider(
                              value: _hdmiBoostLevel.toDouble(),
                              min: 1,
                              max: 11,
                              divisions: 10,
                              label: '$_hdmiBoostLevel',
                              onChanged: (value) => setState(
                                  () => _hdmiBoostLevel = value.round()),
                            ),
                          ),
                          Text('$_hdmiBoostLevel'),
                        ],
                      ),
                    ),
                  SwitchListTile(
                    title: const Text('Ignore EDID'),
                    subtitle: const Text('Ignore display EDID information'),
                    value: _enableHdmiIgnoreEdid,
                    onChanged: (value) =>
                        setState(() => _enableHdmiIgnoreEdid = value),
                  ),
                  SwitchListTile(
                    title: const Text('Ignore CEC'),
                    subtitle: const Text('Ignore HDMI CEC commands'),
                    value: _enableHdmiIgnoreCec,
                    onChanged: (value) =>
                        setState(() => _enableHdmiIgnoreCec = value),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: _hdmiPixelEncoding,
                    decoration: const InputDecoration(
                      labelText: 'HDMI Pixel Encoding',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 0, child: Text('Default')),
                      DropdownMenuItem(value: 1, child: Text('RGB Limited')),
                      DropdownMenuItem(value: 2, child: Text('RGB Full')),
                      DropdownMenuItem(value: 3, child: Text('YUV Limited')),
                      DropdownMenuItem(value: 4, child: Text('YUV Full')),
                    ],
                    onChanged: (value) =>
                        setState(() => _hdmiPixelEncoding = value!),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverlaysTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        'Device Tree Overlays',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      const Icon(Icons.info_outline,
                          color: Colors.blue, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        'Hardware Extensions',
                        style: TextStyle(
                            color: Colors.blue.shade700, fontSize: 12),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Device Tree Overlays enable additional hardware functionality. Only enable overlays for hardware you have connected.',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                  const SizedBox(height: 16),

                  // Audio HATs
                  _buildOverlayCategory('Audio HATs', [
                    'hifiberry-dac',
                    'hifiberry-dacplus',
                    'hifiberry-amp',
                    'iqaudio-dac',
                    'iqaudio-dacplus',
                    'rpi-dac',
                    'rpi-cirrus-wm5102',
                  ]),

                  // Display HATs
                  _buildOverlayCategory('Display HATs', [
                    'pitft22',
                    'pitft28-capacitive',
                    'pitft28-resistive',
                    'pitft35-resistive',
                    'rpi-display',
                    'rpi-backlight',
                    'rpi-ft5406',
                  ]),

                  // Communication
                  _buildOverlayCategory('Communication', [
                    'mcp2515-can0',
                    'mcp2515-can1',
                    'uart0',
                    'uart1',
                    'uart2',
                    'uart3',
                    'uart4',
                    'uart5',
                    'miniuart-bt',
                    'pi3-miniuart-bt',
                    'pi3-disable-bt',
                  ]),

                  // GPIO & Sensors
                  _buildOverlayCategory('GPIO & Sensors', [
                    'gpio-ir',
                    'gpio-ir-tx',
                    'gpio-key',
                    'gpio-poweroff',
                    'gpio-shutdown',
                    'w1-gpio',
                    'w1-gpio-pullup',
                    'rotary-encoder',
                    'pps-gpio',
                    'rpi-sense',
                  ]),

                  // SPI Devices
                  _buildOverlayCategory('SPI Devices', [
                    'spi0-cs',
                    'spi0-hw-cs',
                    'spi1-1cs',
                    'spi1-2cs',
                    'spi1-3cs',
                    'spi2-1cs',
                    'spi2-2cs',
                    'spi2-3cs',
                    'ads7846',
                  ]),

                  // Other
                  _buildOverlayCategory('Other', [
                    'dwc2',
                    'vc4-fkms-v3d',
                    'vc4-kms-v3d',
                    'vc4-kms-v3d-pi4',
                    'pwm',
                    'pwm-2chan',
                    'i2c-rtc',
                    'hd44780-lcd',
                    'lirc-rpi',
                    'wittypi',
                  ]),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverlayCategory(String title, List<String> overlays) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: overlays.map((overlay) {
            final isEnabled = _availableOverlays[overlay] ?? false;
            return FilterChip(
              label: Text(overlay),
              selected: isEnabled,
              onSelected: (selected) {
                setState(() {
                  _availableOverlays[overlay] = selected;
                  if (selected) {
                    if (!_enabledOverlays.contains(overlay)) {
                      _enabledOverlays.add(overlay);
                    }
                  } else {
                    _enabledOverlays.remove(overlay);
                  }
                });
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildAdvancedTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Power Management
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Power & LED Management',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Power LED'),
                    subtitle: const Text('Enable power status LED'),
                    value: _enablePowerLed,
                    onChanged: (value) =>
                        setState(() => _enablePowerLed = value),
                  ),
                  SwitchListTile(
                    title: const Text('Activity LED'),
                    subtitle: const Text('Enable SD card activity LED'),
                    value: _enableActivityLed,
                    onChanged: (value) =>
                        setState(() => _enableActivityLed = value),
                  ),
                  if (_piModel.contains('Pi 4') ||
                      _piModel.contains('Pi 5')) ...[
                    SwitchListTile(
                      title: const Text('Ethernet LED 0'),
                      subtitle: const Text('Enable first ethernet LED'),
                      value: _enableEthLed0,
                      onChanged: (value) =>
                          setState(() => _enableEthLed0 = value),
                    ),
                    SwitchListTile(
                      title: const Text('Ethernet LED 1'),
                      subtitle: const Text('Enable second ethernet LED'),
                      value: _enableEthLed1,
                      onChanged: (value) =>
                          setState(() => _enableEthLed1 = value),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // USB Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'USB Settings',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('High Current USB'),
                    subtitle: const Text(
                        'Enable 1.2A USB current (Pi 1 B+ and newer)'),
                    value: _usbMaxCurrentEnable == 1,
                    onChanged: (value) =>
                        setState(() => _usbMaxCurrentEnable = value ? 1 : 0),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Memory Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Memory Settings',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Configure CMA Memory'),
                    subtitle:
                        Text('Contiguous Memory Allocator: ${_cmaSize}MB'),
                    value: _enableCmaConfig,
                    onChanged: (value) =>
                        setState(() => _enableCmaConfig = value),
                  ),
                  if (_enableCmaConfig)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('CMA Size: '),
                          Expanded(
                            child: Slider(
                              value: _cmaSize.toDouble(),
                              min: 16,
                              max: 256,
                              divisions: 15,
                              label: '${_cmaSize}MB',
                              onChanged: (value) =>
                                  setState(() => _cmaSize = value.round()),
                            ),
                          ),
                          Text('${_cmaSize}MB'),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Thermal Management
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Thermal Management',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Temperature Limit'),
                    subtitle: Text('Hard limit: $_tempLimit°C'),
                    value: _enableTempLimit,
                    onChanged: (value) =>
                        setState(() => _enableTempLimit = value),
                  ),
                  if (_enableTempLimit)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('Temp Limit: '),
                          Expanded(
                            child: Slider(
                              value: _tempLimit.toDouble(),
                              min: 60,
                              max: 90,
                              divisions: 6,
                              label: '$_tempLimit°C',
                              onChanged: (value) =>
                                  setState(() => _tempLimit = value.round()),
                            ),
                          ),
                          Text('$_tempLimit°C'),
                        ],
                      ),
                    ),
                  SwitchListTile(
                    title: const Text('Soft Temperature Limit'),
                    subtitle: Text('Throttling starts at: $_tempSoftLimit°C'),
                    value: _enableTempSoftLimit,
                    onChanged: (value) =>
                        setState(() => _enableTempSoftLimit = value),
                  ),
                  if (_enableTempSoftLimit)
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: Row(
                        children: [
                          const Text('Soft Limit: '),
                          Expanded(
                            child: Slider(
                              value: _tempSoftLimit.toDouble(),
                              min: 50,
                              max: 80,
                              divisions: 6,
                              label: '$_tempSoftLimit°C',
                              onChanged: (value) => setState(
                                  () => _tempSoftLimit = value.round()),
                            ),
                          ),
                          Text('$_tempSoftLimit°C'),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveConfiguration() async {
    try {
      // Create backup
      await widget.commandService.executeCommand(
        widget.deviceId,
        'sudo cp /boot/firmware/config.txt /boot/firmware/config.txt.backup.${DateTime.now().millisecondsSinceEpoch}',
        progressTitle: 'Saving Configuration',
        progressSubtitle: 'Creating backup...',
      );

      // Build configuration updates
      final configUpdates = <String>[];

      // Display settings
      if (!_enableSplashScreen) configUpdates.add('disable_splash=1');
      if (!_enableRainbowSplash) configUpdates.add('disable_rainbow_splash=1');
      if (_enableBootDelay && _bootDelay > 0) {
        configUpdates.add('boot_delay=$_bootDelay');
      }
      if (_enableHdmiSafeMode) configUpdates.add('hdmi_safe=1');
      if (!_enableOverscan) configUpdates.add('disable_overscan=1');

      // Hardware interfaces
      if (_enableCamera) configUpdates.add('camera_auto_detect=1');
      if (_enableSpi) configUpdates.add('dtparam=spi=on');
      if (_enableI2c) configUpdates.add('dtparam=i2c_arm=on');
      if (_enableSerial) configUpdates.add('enable_uart=1');
      if (_enableAudio) configUpdates.add('dtparam=audio=on');

      // GPU settings
      if (_enableGpuMemoryConfig) configUpdates.add('gpu_mem=$_gpuMemorySplit');

      // Overclocking
      if (_enableOverclocking) {
        configUpdates.add('arm_freq=$_armFreq');
        configUpdates.add('gpu_freq=$_gpuFreq');
        configUpdates.add('core_freq=$_coreFreq');
        configUpdates.add('sdram_freq=$_sdramFreq');
        if (_overVoltage > 0) {
          configUpdates.add('over_voltage=${_overVoltage.round()}');
        }
      }

      // HDMI settings
      if (_enableHdmiForceHotplug) configUpdates.add('hdmi_force_hotplug=1');
      configUpdates.add('hdmi_group=$_hdmiGroup');
      configUpdates.add('hdmi_mode=$_hdmiMode');

      // Advanced HDMI settings
      if (_enableHdmiBoost) {
        configUpdates.add('config_hdmi_boost=1');
        configUpdates.add('hdmi_boost=$_hdmiBoostLevel');
      }
      if (_enableHdmiIgnoreEdid) {
        configUpdates.add('hdmi_ignore_edid=0xa5000080');
      }
      if (_enableHdmiIgnoreCec) configUpdates.add('hdmi_ignore_cec=1');
      if (_enableHdmiIgnoreCecInit) configUpdates.add('hdmi_ignore_cec_init=1');
      if (_hdmiPixelEncoding > 0) {
        configUpdates.add('hdmi_pixel_encoding=$_hdmiPixelEncoding');
      }
      if (_hdmiContentType != '0') {
        configUpdates.add('hdmi_content_type=$_hdmiContentType');
      }

      // Power management
      if (!_enablePowerLed) configUpdates.add('dtparam=pwr_led_trigger=none');
      if (!_enableActivityLed) {
        configUpdates.add('dtparam=act_led_trigger=none');
      }
      if (!_enableEthLed0) configUpdates.add('dtparam=eth_led0=4');
      if (!_enableEthLed1) configUpdates.add('dtparam=eth_led1=4');

      // USB settings
      if (_usbMaxCurrentEnable == 1) configUpdates.add('max_usb_current=1');

      // Memory settings
      if (_enableCmaConfig) configUpdates.add('cma=$_cmaSize');

      // Thermal settings
      if (_enableTempLimit) configUpdates.add('temp_limit=$_tempLimit');
      if (_enableTempSoftLimit) {
        configUpdates.add('temp_soft_limit=$_tempSoftLimit');
      }

      // Device tree overlays
      for (final overlay in _enabledOverlays) {
        configUpdates.add('dtoverlay=$overlay');
      }

      // Remove overclocking settings if disabled
      if (!_enableOverclocking) {
        final overclockingKeys = [
          'arm_freq',
          'gpu_freq',
          'core_freq',
          'sdram_freq',
          'over_voltage'
        ];
        for (final key in overclockingKeys) {
          await widget.commandService.executeCommand(
            widget.deviceId,
            'sudo sed -i "/^$key=/d" /boot/firmware/config.txt',
            showProgress: false,
          );
        }
      }

      // Apply configuration updates
      for (final update in configUpdates) {
        final key = update.split('=')[0];
        await widget.commandService.executeCommand(
          widget.deviceId,
          'sudo sed -i "/^$key=/d" /boot/firmware/config.txt && echo "$update" | sudo tee -a /boot/firmware/config.txt',
          showProgress: false,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        _showRebootPrompt();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showRebootPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reboot Required'),
        content: const Text(
            'Configuration changes have been saved successfully. A reboot is required for the changes to take effect. Would you like to reboot now?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Yet'),
          ),
          FilledButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _executeReboot();
            },
            icon: const Icon(Icons.restart_alt, color: Colors.white),
            label: const Text('Reboot Now'),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _executeReboot() async {
    try {
      await widget.commandService.rebootSystem(widget.deviceId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Reboot command sent successfully. Device will lose connection.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Navigate back to Manage Devices page
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // Navigate to main screen and ensure devices tab is selected
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const MainScreen(),
              ),
              (route) => false,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rebooting system: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Utility class for Raspberry Pi Management functions
class RpiManagementUtils {
  /// Extract Raspberry Pi model from system information
  static String extractPiModel(Map<String, dynamic> systemInfo) {
    try {
      // First try to get the exact model from OS info
      if (systemInfo['os'] != null) {
        final osInfo = systemInfo['os'];
        if (osInfo is Map<String, dynamic>) {
          final piModel = osInfo['pi_model'];
          if (piModel is String && piModel.isNotEmpty) {
            return piModel;
          }
        }
      }

      // Fallback to architecture-based detection
      final unameValue = systemInfo['uname'];
      String uname = '';

      if (unameValue is String) {
        uname = unameValue;
      } else if (unameValue is Map<String, dynamic>) {
        // If uname is a map, try to get the machine or system field
        uname = (unameValue['machine'] ??
                unameValue['system'] ??
                unameValue['sysname'] ??
                '')
            .toString();
      } else {
        uname = unameValue?.toString() ?? '';
      }

      if (uname.contains('aarch64')) {
        return 'Raspberry Pi 4/5 (64-bit)';
      } else if (uname.contains('armv7l')) {
        return 'Raspberry Pi 3/4 (32-bit)';
      } else if (uname.contains('armv6l')) {
        return 'Raspberry Pi Zero/1';
      }
    } catch (e) {
      // If there's any type casting error, fall back to generic
      debugPrint('Error extracting Pi model: $e');
    }

    return 'Raspberry Pi (Unknown model)';
  }

  /// Extract short Pi model for badge display (e.g., "RPi 4")
  static String? extractPiModelForBadge(Map<String, dynamic> systemInfo) {
    final fullModel = extractPiModel(systemInfo);

    // Extract model number from full model string
    final match = RegExp(r'Raspberry Pi (\w+(?:\s+\w+)?)', caseSensitive: false)
        .firstMatch(fullModel);
    if (match != null) {
      final modelPart = match.group(1)!;
      // Convert "4 Model B" to "4", "Zero W" to "Zero", etc.
      if (modelPart.startsWith('4')) return 'RPi 4';
      if (modelPart.startsWith('5')) return 'RPi 5';
      if (modelPart.startsWith('3')) return 'RPi 3';
      if (modelPart.startsWith('2')) return 'RPi 2';
      if (modelPart.startsWith('1')) return 'RPi 1';
      if (modelPart.toLowerCase().contains('zero')) return 'RPi Zero';
      return 'RPi $modelPart';
    }

    // Fallback for generic cases
    if (fullModel.contains('4/5')) return 'RPi 4/5';
    if (fullModel.contains('3/4')) return 'RPi 3/4';
    if (fullModel.contains('Zero/1')) return 'RPi Zero/1';

    return 'RPi';
  }
}

/// App Hub Tab Widget
class AppHubTab extends StatefulWidget {
  final AppHubService appHubService;

  const AppHubTab({super.key, required this.appHubService});

  @override
  State<AppHubTab> createState() => _AppHubTabState();
}

class _AppHubTabState extends State<AppHubTab> {
  String _selectedCategory = 'all';
  String _searchQuery = '';
  bool _isLoading = false;
  String _statusFilter = 'all'; // all, installed, updates, not_installed
  bool _showTerminal = false; // Collapsed by default
  final Set<String> _selectedApps = {};
  final Set<String> _expandedCards = {};
  final ScrollController _terminalScrollController = ScrollController();
  final List<String> _terminalOutput = [];
  StreamSubscription<String>? _terminalSubscription;
  Timer? _terminalRefreshTimer;
  Timer? _terminalVisibilityRefreshTimer;
  late RpiCommandService _commandService;

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _commandService = RpiCommandService(appState);
    _loadInstallationStatus();
    _loadPersistedTerminalOutput();
    _setupErrorFixDialog();
    _setupTerminalStream();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh state when returning to this tab
    _refreshAppHubState();
  }

  void _refreshAppHubState() {
    // Load persisted terminal output when returning to App Hub
    _loadPersistedTerminalOutput();

    // Re-setup terminal stream in case it was lost during navigation
    _setupTerminalStream();

    // Only reload installation status if no operations are in progress
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice != null &&
        !widget.appHubService.isAnyOperationInProgress &&
        !appState.isPackageOperationInProgress(selectedDevice.id)) {
      _loadInstallationStatus();
    } else {
      // If operations are in progress, just refresh the UI to show current states
      setState(() {});
    }
  }

  void _setupTerminalStream() {
    // Cancel existing subscription if any
    _terminalSubscription?.cancel();

    // No longer using stream - terminal output is handled through AppState changes
    // The _loadPersistedTerminalOutput method will sync with service storage
  }

  void _setupTerminalVisibilityRefresh() {
    // Cancel existing timer if any
    _terminalVisibilityRefreshTimer?.cancel();

    // Set up a 5-second refresh timer when terminal is visible
    _terminalVisibilityRefreshTimer =
        Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _showTerminal) {
        // Force refresh terminal output to prevent UI getting stuck
        final currentServiceOutput = widget.appHubService.terminalOutput;
        if (currentServiceOutput.isNotEmpty) {
          setState(() {
            _terminalOutput.clear();
            _terminalOutput.addAll(currentServiceOutput);
            _scrollTerminalToBottom();
          });
        }
      } else if (!_showTerminal) {
        // Stop refreshing when terminal is not visible
        timer.cancel();
      }
    });
  }

  void _setupErrorFixDialog() {
    final appState = Provider.of<AppState>(context, listen: false);
    appState.showErrorFixDialog = _showErrorFixDialog;
  }

  void _loadPersistedTerminalOutput() {
    // Load any existing terminal output from the service
    final persistedOutput = widget.appHubService.terminalOutput;
    if (persistedOutput.isNotEmpty) {
      setState(() {
        _terminalOutput.clear();
        _terminalOutput.addAll(persistedOutput);
        // Don't automatically show terminal - user must click to view
      });
      // Auto-scroll to bottom when loading persisted output (if terminal is visible)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_showTerminal) {
          _scrollTerminalToBottom();
        }
      });
    }

    // Set up a periodic timer to refresh terminal output from the service
    _terminalRefreshTimer?.cancel();
    _terminalRefreshTimer =
        Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted && widget.appHubService.isAnyOperationInProgress) {
        final currentServiceOutput = widget.appHubService.terminalOutput;
        if (currentServiceOutput.length != _terminalOutput.length) {
          setState(() {
            _terminalOutput.clear();
            _terminalOutput.addAll(currentServiceOutput);
            if (_showTerminal) {
              _scrollTerminalToBottom();
            }
          });
        }
      } else if (!widget.appHubService.isAnyOperationInProgress) {
        // Stop refreshing when no operation is in progress
        timer.cancel();
      }
    });

    // Set up a separate timer for periodic refresh when terminal is visible
    _setupTerminalVisibilityRefresh();
  }

  Future<void> _loadInstallationStatus() async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    // Don't check status if any local operation is in progress
    if (widget.appHubService.isAnyOperationInProgress) {
      debugPrint('Skipping status check - local operation in progress');
      return;
    }

    // Don't check status if apt is running on the remote device
    if (appState.isPackageOperationInProgress(selectedDevice.id)) {
      debugPrint('Skipping status check - apt operation in progress on device');
      return;
    }

    setState(() => _isLoading = true);

    await widget.appHubService.checkInstallationStatus(selectedDevice.id);

    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final repository = widget.appHubService.repository;

    if (repository == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text('Failed to load App Hub repository'),
          ],
        ),
      );
    }

    return Consumer<AppState>(
      builder: (context, appState, child) {
        final selectedDevice = appState.selectedDevice;

        if (selectedDevice == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.device_unknown, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No device selected'),
                SizedBox(height: 8),
                Text('Please select a Raspberry Pi device to browse apps'),
              ],
            ),
          );
        }

        return Column(
          children: [
            _buildHeader(repository),
            _buildSearchAndFilter(repository),
            Expanded(
              child: _showTerminal
                  ? Column(
                      children: [
                        Expanded(flex: 2, child: _buildAppGrid(repository)),
                        _buildTerminal(),
                      ],
                    )
                  : _buildAppGrid(repository),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader(AppHubRepository repository) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Main header row
          Row(
            children: [
              const Icon(Icons.apps, size: 32, color: Colors.blue),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${repository.repository.description} - ${repository.repository.source}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons
              Row(
                children: [
                  // Terminal toggle
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _showTerminal = !_showTerminal;
                        if (_showTerminal) {
                          // Start visibility refresh when terminal is shown
                          _setupTerminalVisibilityRefresh();
                        } else {
                          // Stop visibility refresh when terminal is hidden
                          _terminalVisibilityRefreshTimer?.cancel();
                        }
                      });
                    },
                    icon: Icon(_showTerminal ? Icons.terminal : Icons.code),
                    tooltip: _showTerminal ? 'Hide Terminal' : 'Show Terminal',
                  ),
                  // Repository manager (developer mode only)
                  Consumer<AppState>(
                    builder: (context, appState, child) {
                      if (appState.developerModeEnabled) {
                        final isAnyOperationInProgress =
                            widget.appHubService.isAnyOperationInProgress;
                        return IconButton(
                          onPressed: isAnyOperationInProgress
                              ? null
                              : _openRepositoryEditor,
                          icon: Icon(
                            Icons.edit_note,
                            color:
                                isAnyOperationInProgress ? Colors.grey : null,
                          ),
                          tooltip: isAnyOperationInProgress
                              ? 'Repository management disabled during operations'
                              : 'Manage Repos',
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                  // Cancel button (only show when installation/removal/update is in progress, not during checking)
                  if (widget.appHubService.isAnyInstallationInProgress)
                    Consumer<AppState>(
                      builder: (context, appState, child) {
                        final isCancelled = widget.appHubService.isCancelled;
                        return ElevatedButton.icon(
                          onPressed: isCancelled
                              ? null
                              : () {
                                  widget.appHubService.cancelCurrentOperation();
                                },
                          icon: Icon(
                              isCancelled
                                  ? Icons.hourglass_empty
                                  : Icons.cancel,
                              color: Colors.white),
                          label: Text(isCancelled ? 'Cancelling...' : 'Cancel'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isCancelled
                                ? Colors.grey
                                : const Color(0xFFE53E3E), // Better red shade
                            foregroundColor: Colors.white,
                          ),
                        );
                      },
                    ),
                  if (widget.appHubService.isAnyInstallationInProgress)
                    const SizedBox(width: 8),
                  // Check orphaned packages button
                  IconButton(
                    onPressed: (_isLoading ||
                            widget.appHubService.isAnyOperationInProgress)
                        ? null
                        : _checkOrphanedPackages,
                    icon: const Icon(Icons.cleaning_services),
                    tooltip: 'Check for orphaned packages',
                  ),
                  const SizedBox(width: 8),
                  // Refresh button
                  ElevatedButton.icon(
                    onPressed: (_isLoading ||
                            widget.appHubService.isAnyOperationInProgress)
                        ? null
                        : _loadInstallationStatus,
                    icon: (_isLoading ||
                            widget.appHubService.isAnyOperationInProgress)
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: colorScheme.onPrimary,
                            ),
                          )
                        : const Icon(Icons.refresh),
                    label: Text((_isLoading ||
                            widget.appHubService.isAnyOperationInProgress)
                        ? 'Working...'
                        : 'Refresh'),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Package operation warning
          Consumer<AppState>(
            builder: (context, appState, child) {
              final selectedDevice = appState.selectedDevice;
              if (selectedDevice != null &&
                  appState.isPackageOperationInProgress(selectedDevice.id)) {
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    border:
                        Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Package management operation detected on ${selectedDevice.name}. App Hub operations are temporarily disabled.',
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          // Filters row
          Row(
            children: [
              // Status filter
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(
                      color: colorScheme.outline.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                  color: colorScheme.surface,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _statusFilter,
                    isDense: true,
                    icon: Icon(Icons.filter_list, color: colorScheme.onSurface),
                    style: TextStyle(color: colorScheme.onSurface),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Apps')),
                      DropdownMenuItem(
                          value: 'installed', child: Text('Installed')),
                      DropdownMenuItem(
                          value: 'updates', child: Text('Updates Available')),
                      DropdownMenuItem(
                          value: 'not_installed', child: Text('Not Installed')),
                    ],
                    onChanged: (value) {
                      setState(() => _statusFilter = value ?? 'all');
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Multi-select actions
              if (_selectedApps.isNotEmpty) ...[
                _buildBulkActionButton(colorScheme),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _clearSelection,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.surfaceContainerHighest,
                    foregroundColor: colorScheme.onSurface,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(AppHubRepository repository) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search applications...',
                prefixIcon: Icon(Icons.search, color: colorScheme.outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                filled: true,
                fillColor: colorScheme.surface,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
          ),
          const SizedBox(width: 16),
          Container(
            height: 48, // Match search field height
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border:
                  Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
              color: colorScheme.surface,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedCategory,
                isDense: true,
                icon: Icon(Icons.arrow_drop_down, color: colorScheme.onSurface),
                style: TextStyle(color: colorScheme.onSurface),
                items: [
                  DropdownMenuItem(
                    value: 'all',
                    child: Row(
                      children: [
                        Icon(Icons.apps,
                            size: 16, color: colorScheme.onSurface),
                        const SizedBox(width: 8),
                        Text('All Categories',
                            style: TextStyle(color: colorScheme.onSurface)),
                      ],
                    ),
                  ),
                  ...repository.categories.map((category) => DropdownMenuItem(
                        value: category.id,
                        child: Row(
                          children: [
                            Icon(
                              SharedServiceRegistry()
                                      .getIconData(category.icon) ??
                                  Icons.category,
                              size: 16,
                              color: colorScheme.onSurface,
                            ),
                            const SizedBox(width: 8),
                            Text(category.name,
                                style: TextStyle(color: colorScheme.onSurface)),
                          ],
                        ),
                      )),
                ],
                onChanged: (value) {
                  setState(() => _selectedCategory = value ?? 'all');
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppGrid(AppHubRepository repository) {
    List<AppHubApplication> apps = repository.applications;

    // Filter by category
    if (_selectedCategory != 'all') {
      apps = apps.where((app) => app.category == _selectedCategory).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      apps = widget.appHubService.searchApplications(_searchQuery);
      if (_selectedCategory != 'all') {
        apps = apps.where((app) => app.category == _selectedCategory).toList();
      }
    }

    // Filter by status
    if (_statusFilter != 'all') {
      apps = apps.where((app) {
        final status = widget.appHubService.installationStatus[app.id] ??
            AppInstallationStatus.unknown;
        switch (_statusFilter) {
          case 'installed':
            return status == AppInstallationStatus.installed;
          case 'updates':
            return status == AppInstallationStatus.updateAvailable;
          case 'not_installed':
            return status == AppInstallationStatus.notInstalled ||
                status == AppInstallationStatus.unknown;
          default:
            return true;
        }
      }).toList();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive column count based on screen width
        int crossAxisCount;
        if (constraints.maxWidth > 2000) {
          crossAxisCount = 10;
        } else if (constraints.maxWidth > 1800) {
          crossAxisCount = 9;
        } else if (constraints.maxWidth > 1600) {
          crossAxisCount = 8;
        } else if (constraints.maxWidth > 1400) {
          crossAxisCount = 7;
        } else if (constraints.maxWidth > 1200) {
          crossAxisCount = 6;
        } else if (constraints.maxWidth > 1000) {
          crossAxisCount = 5;
        } else if (constraints.maxWidth > 800) {
          crossAxisCount = 4;
        } else if (constraints.maxWidth > 600) {
          crossAxisCount = 3;
        } else {
          crossAxisCount = 2;
        }

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: 1.1, // Shorter cards like in the mockup
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: apps.length,
          itemBuilder: (context, index) => _buildAppCard(apps[index]),
        );
      },
    );
  }

  Widget _buildAppCard(AppHubApplication app) {
    final status = widget.appHubService.installationStatus[app.id] ??
        AppInstallationStatus.unknown;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedApps.contains(app.id);
    final isExpanded = _expandedCards.contains(app.id);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (isExpanded) {
            _expandedCards.remove(app.id);
          } else {
            _expandedCards.add(app.id);
          }
        });
      },
      onDoubleTap: () => _showAppSettings(app),
      onLongPress: () {
        setState(() {
          if (isSelected) {
            _selectedApps.remove(app.id);
          } else {
            _selectedApps.add(app.id);
          }
        });
      },
      child: Card(
        elevation: isSelected ? 4 : 1,
        color: isSelected
            ? colorScheme.primaryContainer.withValues(alpha: 0.3)
            : null,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: isExpanded ? MainAxisSize.max : MainAxisSize.min,
            children: [
              // App icon and name
              Row(
                children: [
                  Stack(
                    children: [
                      // App logo/icon
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colorScheme.outline.withValues(alpha: 0.2),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: _buildAppLogo(app),
                        ),
                      ),
                      if (isSelected)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: _getActionColor(status),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _getActionIcon(status),
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          app.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          app.size,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withValues(
                                alpha: 0.8), // Darker for better readability
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              // Description
              if (isExpanded)
                Flexible(
                  child: Text(
                    app.description,
                    style: theme.textTheme.bodySmall,
                  ),
                )
              else
                Text(
                  app.description,
                  style: theme.textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 6),
              // Install button
              _buildInstallButton(app, status),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getAppIcon(String category) {
    switch (category) {
      case 'appearance':
        return Icons.palette;
      case 'creative':
        return Icons.brush;
      case 'engineering':
        return Icons.engineering;
      case 'games':
        return Icons.games;
      case 'internet':
        return Icons.public;
      case 'browsers':
        return Icons.web;
      case 'communication':
        return Icons.chat;
      case 'multimedia':
        return Icons.movie;
      case 'office':
        return Icons.work;
      case 'programming':
        return Icons.code;
      case 'system':
        return Icons.settings;
      case 'terminals':
        return Icons.terminal;
      case 'tools':
        return Icons.build;
      case 'crypto':
        return Icons.currency_bitcoin;
      case 'emulation':
        return Icons.computer;
      default:
        return Icons.apps;
    }
  }

  // Get action icon based on app status
  IconData _getActionIcon(AppInstallationStatus status) {
    switch (status) {
      case AppInstallationStatus.installed:
        return Icons.delete; // Will remove
      case AppInstallationStatus.updateAvailable:
        return Icons.update; // Will update
      case AppInstallationStatus.notInstalled:
      case AppInstallationStatus.unknown:
      default:
        return Icons.download; // Will install
    }
  }

  // Get action color based on app status - softer, more pleasant colors
  Color _getActionColor(AppInstallationStatus status) {
    switch (status) {
      case AppInstallationStatus.installed:
        return const Color(0xFFEF4444); // Softer red for remove
      case AppInstallationStatus.updateAvailable:
        return const Color(0xFFF59E0B); // Softer amber for update
      case AppInstallationStatus.notInstalled:
      case AppInstallationStatus.unknown:
      default:
        return const Color(0xFF10B981); // Softer emerald for install
    }
  }

  Widget _buildInstallButton(
      AppHubApplication app, AppInstallationStatus status) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isAnyOperationInProgress =
        widget.appHubService.isAnyOperationInProgress;

    switch (status) {
      case AppInstallationStatus.checking:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: colorScheme.primary,
                ),
              ),
            ),
          ),
        );
      case AppInstallationStatus.installed:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: IconButton(
            onPressed: isAnyOperationInProgress ? null : () => _removeApp(app),
            icon: Icon(
              Icons.delete_outline,
              size: 18,
              color: isAnyOperationInProgress
                  ? colorScheme.onSurface.withValues(alpha: 0.4)
                  : const Color(0xFFEF4444),
            ),
            style: IconButton.styleFrom(
              backgroundColor: isAnyOperationInProgress
                  ? colorScheme.surfaceContainerHighest
                  : const Color(0xFFEF4444).withValues(alpha: 0.1),
              minimumSize: const Size(32, 32),
              maximumSize: const Size(32, 32),
              padding: EdgeInsets.zero,
            ),
            tooltip: isAnyOperationInProgress ? 'Please wait...' : 'Remove',
          ),
        );
      case AppInstallationStatus.installing:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: const Color(0xFF10B981),
                ),
              ),
            ),
          ),
        );
      case AppInstallationStatus.removing:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: const Color(0xFFEF4444),
                ),
              ),
            ),
          ),
        );
      case AppInstallationStatus.updateAvailable:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: IconButton(
            onPressed: isAnyOperationInProgress ? null : () => _updateApp(app),
            icon: Icon(
              Icons.upgrade,
              size: 18,
              color: isAnyOperationInProgress
                  ? colorScheme.onSurface.withValues(alpha: 0.4)
                  : const Color(0xFFF59E0B),
            ),
            style: IconButton.styleFrom(
              backgroundColor: isAnyOperationInProgress
                  ? colorScheme.surfaceContainerHighest
                  : const Color(0xFFF59E0B).withValues(alpha: 0.1),
              minimumSize: const Size(32, 32),
              maximumSize: const Size(32, 32),
              padding: EdgeInsets.zero,
            ),
            tooltip: isAnyOperationInProgress ? 'Please wait...' : 'Update',
          ),
        );
      case AppInstallationStatus.updating:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: const Color(0xFFF59E0B),
                ),
              ),
            ),
          ),
        );
      case AppInstallationStatus.failed:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: IconButton(
            onPressed: isAnyOperationInProgress ? null : () => _installApp(app),
            icon: Icon(
              Icons.refresh,
              size: 18,
              color: isAnyOperationInProgress
                  ? colorScheme.onSurface.withValues(alpha: 0.4)
                  : const Color(0xFFF59E0B),
            ),
            style: IconButton.styleFrom(
              backgroundColor: isAnyOperationInProgress
                  ? colorScheme.surfaceContainerHighest
                  : const Color(0xFFF59E0B).withValues(alpha: 0.1),
              minimumSize: const Size(32, 32),
              maximumSize: const Size(32, 32),
              padding: EdgeInsets.zero,
            ),
            tooltip: isAnyOperationInProgress ? 'Please wait...' : 'Retry',
          ),
        );
      default:
        return Container(
          height: 32,
          alignment: Alignment.centerRight,
          child: IconButton(
            onPressed: isAnyOperationInProgress ? null : () => _installApp(app),
            icon: Icon(
              Icons.download,
              size: 18,
              color: isAnyOperationInProgress
                  ? colorScheme.onSurface.withValues(alpha: 0.4)
                  : const Color(0xFF10B981),
            ),
            style: IconButton.styleFrom(
              backgroundColor: isAnyOperationInProgress
                  ? colorScheme.surfaceContainerHighest
                  : const Color(0xFF10B981).withValues(alpha: 0.1),
              minimumSize: const Size(32, 32),
              maximumSize: const Size(32, 32),
              padding: EdgeInsets.zero,
            ),
            tooltip: isAnyOperationInProgress ? 'Please wait...' : 'Install',
          ),
        );
    }
  }

  Future<void> _installApp(AppHubApplication app) async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    setState(() {
      _isLoading = true;
      if (_showTerminal) {
        // Clear terminal output at start of new operation
        widget.appHubService.clearTerminalOutput();
        _terminalOutput.clear();

        final startCommand =
            'pi@${selectedDevice.name}:~ \$ # Installing ${app.name}';
        _terminalOutput.add(startCommand);
        widget.appHubService.addTerminalOutput(startCommand);
        _scrollTerminalToBottom();
      }
    });

    bool rebootRequired = false;

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    // Set up reboot required callback
    void onRebootRequired(bool required) {
      rebootRequired = required;
    }

    final success = await widget.appHubService.installApplicationWithOutput(
      selectedDevice.id,
      app,
      onOutput: onOutput,
      onRebootRequired: onRebootRequired,
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${app.name} installed successfully')),
        );

        // Show reboot dialog if required
        if (rebootRequired) {
          _showRebootDialog(app.name);
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to install ${app.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removeApp(AppHubApplication app) async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    setState(() {
      _isLoading = true;
      if (_showTerminal) {
        // Clear terminal output at start of new operation
        widget.appHubService.clearTerminalOutput();
        _terminalOutput.clear();

        final startCommand =
            'pi@${selectedDevice.name}:~ \$ # Removing ${app.name}';
        _terminalOutput.add(startCommand);
        _scrollTerminalToBottom();
      }
    });

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    final success = await widget.appHubService.removeApplicationWithOutput(
      selectedDevice.id,
      app,
      onOutput: onOutput,
      onAutoRemoveDetected: (data) {
        if (mounted) {
          _showAutoRemoveDialog(
            data['packages'] as List<String>,
            selectedDevice.id,
            diskSpace: data['diskSpace'] as String,
          );
        }
      },
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${app.name} removed successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove ${app.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateApp(AppHubApplication app) async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    setState(() {
      _isLoading = true;
      if (_showTerminal) {
        // Clear terminal output at start of new operation
        widget.appHubService.clearTerminalOutput();
        _terminalOutput.clear();

        final startCommand =
            'pi@${selectedDevice.name}:~ \$ # Updating ${app.name}';
        _terminalOutput.add(startCommand);
        _scrollTerminalToBottom();
      }
    });

    bool rebootRequired = false;

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    // Set up reboot required callback
    void onRebootRequired(bool required) {
      rebootRequired = required;
    }

    final success = await widget.appHubService.updateApplicationWithOutput(
      selectedDevice.id,
      app,
      onOutput: onOutput,
      onRebootRequired: onRebootRequired,
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${app.name} updated successfully')),
        );

        // Show reboot dialog if required
        if (rebootRequired) {
          _showRebootDialog(app.name);
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update ${app.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Terminal widget with real functionality
  Widget _buildTerminal() {
    final theme = Theme.of(context);

    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2D2D), // Dark terminal background
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Terminal header with macOS-style design
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFF3C3C3C), // Darker header
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                // macOS-style traffic lights
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Color(0xFFFF5F57), // Red
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Color(0xFFFFBD2E), // Yellow
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Color(0xFF28CA42), // Green
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 12),
                // Terminal title
                Text(
                  'pi@raspberrypi: ~',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'monospace',
                  ),
                ),
                const Spacer(),
                // Close button
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showTerminal = false;
                      // Don't clear terminal output when hiding
                      _terminalVisibilityRefreshTimer?.cancel();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Terminal content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              child: ListView.builder(
                controller: _terminalScrollController,
                itemCount: _terminalOutput.length,
                itemBuilder: (context, index) {
                  return SelectableText(
                    _terminalOutput[index],
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: 'monospace',
                      fontSize: 14,
                      height: 1.4,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Repository manager
  void _openRepositoryEditor() async {
    if (mounted) {
      Navigator.of(context)
          .push(
        MaterialPageRoute(
          builder: (context) => RepositoryEditor(
            appHubService: widget.appHubService,
          ),
        ),
      )
          .then((_) {
        // Refresh the UI when returning from the repository manager
        setState(() {});
      });
    }
  }

  // Build smart bulk action button
  Widget _buildBulkActionButton(ColorScheme colorScheme) {
    final repository = widget.appHubService.repository;
    if (repository == null) return const SizedBox.shrink();

    final selectedApps = repository.applications
        .where((app) => _selectedApps.contains(app.id))
        .toList();

    int installCount = 0;
    int removeCount = 0;
    int updateCount = 0;

    for (final app in selectedApps) {
      final status = widget.appHubService.installationStatus[app.id] ??
          AppInstallationStatus.unknown;

      switch (status) {
        case AppInstallationStatus.installed:
          removeCount++;
          break;
        case AppInstallationStatus.updateAvailable:
          updateCount++;
          break;
        case AppInstallationStatus.notInstalled:
        case AppInstallationStatus.unknown:
          installCount++;
          break;
        default:
          // For other statuses, default to install
          installCount++;
          break;
      }
    }

    // Build action summary text
    List<String> actions = [];
    if (installCount > 0) actions.add('Install $installCount');
    if (removeCount > 0) actions.add('Remove $removeCount');
    if (updateCount > 0) actions.add('Update $updateCount');

    final actionText = actions.join(' • ');

    // Choose appropriate icon based on primary action
    IconData icon;
    if (installCount >= removeCount && installCount >= updateCount) {
      icon = Icons.download;
    } else if (removeCount >= updateCount) {
      icon = Icons.delete;
    } else {
      icon = Icons.update;
    }

    return ElevatedButton.icon(
      onPressed: widget.appHubService.isAnyOperationInProgress
          ? null
          : _applyBulkActions,
      icon: Icon(icon),
      label: Text(actionText),
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
      ),
    );
  }

  // Apply bulk actions based on app status
  Future<void> _applyBulkActions() async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null || _selectedApps.isEmpty) return;

    // Check if any operation is already in progress
    if (widget.appHubService.isAnyOperationInProgress) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Another operation is already in progress. Please wait for it to complete.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    final repository = widget.appHubService.repository;
    if (repository == null) return;

    final selectedApps = repository.applications
        .where((app) => _selectedApps.contains(app.id))
        .toList();

    // Categorize apps by action needed
    final appsToInstall = <AppHubApplication>[];
    final appsToRemove = <AppHubApplication>[];
    final appsToUpdate = <AppHubApplication>[];

    for (final app in selectedApps) {
      final status = widget.appHubService.installationStatus[app.id] ??
          AppInstallationStatus.unknown;

      switch (status) {
        case AppInstallationStatus.installed:
          appsToRemove.add(app);
          break;
        case AppInstallationStatus.updateAvailable:
          appsToUpdate.add(app);
          break;
        case AppInstallationStatus.notInstalled:
        case AppInstallationStatus.unknown:
          appsToInstall.add(app);
          break;
        default:
          // For other statuses, default to install
          appsToInstall.add(app);
          break;
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
        if (_showTerminal) {
          // Clear terminal output at start of new operation
          _terminalOutput.clear();
          widget.appHubService.clearTerminalOutput();

          final startCommand =
              'pi@${selectedDevice.name}:~ \$ # Applying actions to ${selectedApps.length} applications';
          widget.appHubService.addTerminalOutput(startCommand);
          _scrollTerminalToBottom();
        }
      });
    }

    bool anyRebootRequired = false;
    int successCount = 0;
    int totalCount = selectedApps.length;

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    // Set up reboot required callback
    void onRebootRequired(bool required) {
      if (required) anyRebootRequired = true;
    }

    // Process installations
    for (final app in appsToInstall) {
      final success = await widget.appHubService.installApplicationWithOutput(
        selectedDevice.id,
        app,
        onOutput: onOutput,
        onRebootRequired: onRebootRequired,
      );
      if (success) successCount++;
    }

    // Process removals
    for (final app in appsToRemove) {
      final success = await widget.appHubService.removeApplicationWithOutput(
        selectedDevice.id,
        app,
        onOutput: onOutput,
      );
      if (success) successCount++;
    }

    // Process updates
    for (final app in appsToUpdate) {
      final success = await widget.appHubService.updateApplicationWithOutput(
        selectedDevice.id,
        app,
        onOutput: onOutput,
        onRebootRequired: onRebootRequired,
      );
      if (success) successCount++;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _clearSelection();
    }

    if (mounted) {
      // Build result message
      List<String> results = [];
      if (appsToInstall.isNotEmpty) {
        results.add('${appsToInstall.length} installed');
      }
      if (appsToRemove.isNotEmpty) {
        results.add('${appsToRemove.length} removed');
      }
      if (appsToUpdate.isNotEmpty) {
        results.add('${appsToUpdate.length} updated');
      }

      final resultText = results.join(', ');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Completed $successCount of $totalCount operations ($resultText)'),
          backgroundColor:
              successCount == totalCount ? Colors.green : Colors.orange,
        ),
      );

      // Show reboot dialog if any app requires reboot
      if (anyRebootRequired) {
        _showRebootDialog('modified applications');
      }
    }
  }

  // Clear selection
  void _clearSelection() {
    if (mounted) {
      setState(() {
        _selectedApps.clear();
      });
    }
  }

  // Show error fix dialog
  Future<bool> _showErrorFixDialog(String error, String fix) async {
    final completer = Completer<bool>();

    if (!mounted) return false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.build,
          color: Colors.orange,
          size: 48,
        ),
        title: const Text('Installation Error Detected'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'The following error was detected:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Text(
                error,
                style: TextStyle(
                  fontFamily: 'monospace',
                  color: Colors.red.shade800,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Would you like to automatically apply this fix?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                fix,
                style: TextStyle(
                  fontFamily: 'monospace',
                  color: Colors.blue.shade800,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              completer.complete(false);
            },
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              completer.complete(true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
            child: const Text('Apply Fix'),
          ),
        ],
      ),
    );

    return completer.future;
  }

  // Show auto-remove dialog
  void _showAutoRemoveDialog(List<String> packages, String deviceId,
      {String? diskSpace}) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.cleaning_services, color: colorScheme.primary),
            const SizedBox(width: 8),
            const Text('Auto-remove Packages'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Found ${packages.length} packages that are no longer required:',
                style: theme.textTheme.bodyLarge
                    ?.copyWith(fontWeight: FontWeight.w500),
              ),
              if (diskSpace != null && diskSpace.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.storage, color: colorScheme.primary, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Will free $diskSpace of disk space',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                      color: colorScheme.outline.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: packages.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      child: Text(
                        '• ${packages[index]}',
                        style: theme.textTheme.bodySmall
                            ?.copyWith(fontFamily: 'monospace'),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Would you like to automatically remove these packages to free up disk space?',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: colorScheme.onSurface,
            ),
            child: const Text('Keep Packages'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _executeAutoRemove(deviceId);
            },
            style: FilledButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
            ),
            child: const Text('Auto-remove'),
          ),
        ],
      ),
    );
  }

  // Check for orphaned packages manually
  Future<void> _checkOrphanedPackages() async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    setState(() {
      _isLoading = true;
      if (_showTerminal) {
        // Clear terminal output at start of new operation
        widget.appHubService.clearTerminalOutput();
        _terminalOutput.clear();

        final startCommand =
            'pi@${selectedDevice.name}:~ \$ # Checking for orphaned packages';
        _terminalOutput.add(startCommand);
        _scrollTerminalToBottom();
      }
    });

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    // Use the same method as the auto-remove check
    bool foundOrphanedPackages = false;
    try {
      await widget.appHubService.checkForAutoRemove(
        selectedDevice.id,
        onOutput: onOutput,
        onAutoRemoveDetected: (data) {
          foundOrphanedPackages = true;
          if (mounted) {
            _showAutoRemoveDialog(
              data['packages'] as List<String>,
              selectedDevice.id,
              diskSpace: data['diskSpace'] as String,
            );
          }
        },
      );
    } catch (e) {
      if (mounted) {
        onOutput('❌ Error checking for orphaned packages: $e');
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      // Show snackbar if no orphaned packages were found
      if (!foundOrphanedPackages) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('No orphaned packages found - system is clean!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Execute auto-remove
  Future<void> _executeAutoRemove(String deviceId) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      if (_showTerminal) {
        final startCommand = 'pi@$deviceId:~ \$ # Auto-removing packages';
        _terminalOutput.add(startCommand);
        _scrollTerminalToBottom();
      }
    });

    // Set up terminal output callback
    void onOutput(String output) {
      if (mounted) {
        setState(() {
          _terminalOutput.add(output);
          if (_showTerminal) {
            _scrollTerminalToBottom();
          }
        });
      }
      // Also sync with service for persistence
      widget.appHubService.addTerminalOutput(output);
    }

    final success = await widget.appHubService.executeAutoRemove(
      deviceId,
      onOutput: onOutput,
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Auto-remove completed successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Auto-remove failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show reboot dialog
  void _showRebootDialog(String appName) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.restart_alt,
          color: Colors.orange,
          size: 48,
        ),
        title: const Text('Reboot Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$appName has been installed successfully, but requires a system reboot to function properly.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Would you like to reboot the system now?',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Yet'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _rebootSystem();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reboot Now'),
          ),
        ],
      ),
    );
  }

  // Reboot system
  Future<void> _rebootSystem() async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null) return;

    try {
      final commandService = RpiCommandService(appState);
      await commandService.rebootSystem(selectedDevice.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reboot command sent successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reboot system: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Scroll terminal to bottom
  void _scrollTerminalToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_terminalScrollController.hasClients) {
        _terminalScrollController.animateTo(
          _terminalScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Build app logo widget
  Widget _buildAppLogo(AppHubApplication app) {
    // First, check if the app has a custom icon path from the icon picker
    if (app.iconPath != null &&
        app.iconPath!.isNotEmpty &&
        app.iconPath != 'null') {
      final file = File(app.iconPath!);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackIcon(app);
          },
        );
      }
    }

    // Map of app IDs to their logo assets or network images
    final Map<String, String> appLogos = {
      'conky':
          'https://raw.githubusercontent.com/brndnmtthws/conky/main/logo/conky-logomark-violet.png',
      'gimp':
          'https://upload.wikimedia.org/wikipedia/commons/4/45/The_GIMP_icon_-_gnome.svg',
      'kicad':
          'https://upload.wikimedia.org/wikipedia/commons/5/59/KiCad-Logo.svg',
      'minecraft-java':
          'https://upload.wikimedia.org/wikipedia/en/5/51/Minecraft_cover.png',
      'firefox':
          'https://upload.wikimedia.org/wikipedia/commons/a/a0/Firefox_logo%2C_2019.svg',
      'legcord':
          'https://raw.githubusercontent.com/legcord/legcord/main/assets/ac_plug_colored.png',
      'obs-studio':
          'https://upload.wikimedia.org/wikipedia/commons/d/d3/OBS_Studio_Logo.svg',
      'libreoffice':
          'https://upload.wikimedia.org/wikipedia/commons/8/8d/LibreOffice_Initial-Artwork-Logo_ColorLogoBasic_500px.png',
      'vscode':
          'https://upload.wikimedia.org/wikipedia/commons/9/9a/Visual_Studio_Code_1.35_icon.svg',
      'htop': 'https://raw.githubusercontent.com/htop-dev/htop/main/htop.png',
      'alacritty':
          'https://raw.githubusercontent.com/alacritty/alacritty/master/extra/logo/alacritty-term.svg',
      'nodejs':
          'https://upload.wikimedia.org/wikipedia/commons/d/d9/Node.js_logo.svg',
      'docker':
          'https://upload.wikimedia.org/wikipedia/commons/4/4e/Docker_%28container_engine%29_logo.svg',
    };

    final logoUrl = appLogos[app.id];

    if (logoUrl != null) {
      if (logoUrl.startsWith('http')) {
        // Network image
        return Image.network(
          logoUrl,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackIcon(app);
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                strokeWidth: 2,
              ),
            );
          },
        );
      } else {
        // Asset image
        return Image.asset(
          logoUrl,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackIcon(app);
          },
        );
      }
    }

    return _buildFallbackIcon(app);
  }

  // Fallback icon when logo is not available
  Widget _buildFallbackIcon(AppHubApplication app) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Try to get the specific app icon first, then fall back to category icon
    IconData iconData = SharedServiceRegistry().getIconData(app.icon) ??
        _getAppIcon(app.category);

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primaryContainer,
            colorScheme.primaryContainer.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Icon(
        iconData,
        color: colorScheme.primary,
        size: 24,
      ),
    );
  }

  // Show app settings dialog
  void _showAppSettings(AppHubApplication app) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final status = widget.appHubService.installationStatus[app.id] ??
        AppInstallationStatus.unknown;
    final isInstalled = status == AppInstallationStatus.installed;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: _buildAppLogo(app),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isInstalled ? '${app.name} Settings' : app.name,
                style: theme.textTheme.titleLarge,
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 600,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // App info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Description:', style: theme.textTheme.labelMedium),
                    const SizedBox(height: 4),
                    Text(app.description, style: theme.textTheme.bodySmall),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text('Size: ', style: theme.textTheme.labelMedium),
                        Text(app.size, style: theme.textTheme.bodySmall),
                        const Spacer(),
                        Text('Category: ', style: theme.textTheme.labelMedium),
                        Text(app.category, style: theme.textTheme.bodySmall),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Show different content based on installation status
              if (isInstalled) ...[
                // Settings options for installed apps
                Text('Application Settings',
                    style: theme.textTheme.titleMedium),
                const SizedBox(height: 8),

                // Start on boot
                _AppSettingsSwitchTile(
                  title: 'Start on Boot',
                  subtitle:
                      'Automatically start this application when the system boots',
                  app: app,
                  settingType: AppSettingType.startOnBoot,
                  commandService: _commandService,
                ),

                // Auto-update
                _AppSettingsSwitchTile(
                  title: 'Auto-Update',
                  subtitle:
                      'Automatically update this application when updates are available',
                  app: app,
                  settingType: AppSettingType.autoUpdate,
                  commandService: _commandService,
                ),

                // Desktop shortcut
                _AppSettingsSwitchTile(
                  title: 'Desktop Shortcut',
                  subtitle: 'Create a desktop shortcut for this application',
                  app: app,
                  settingType: AppSettingType.desktopShortcut,
                  commandService: _commandService,
                ),
              ] else ...[
                // Installation info for non-installed apps
                Text('Installation Commands',
                    style: theme.textTheme.titleMedium),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: colorScheme.outline.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Install Type: ${app.installType}',
                          style: theme.textTheme.labelMedium),
                      const SizedBox(height: 8),
                      Text('Commands:', style: theme.textTheme.labelMedium),
                      const SizedBox(height: 4),
                      ...app.installCommands.map((cmd) => Padding(
                            padding: const EdgeInsets.only(left: 8, bottom: 2),
                            child: Text('• $cmd',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  fontFamily: 'monospace',
                                  fontSize: 11,
                                )),
                          )),
                      if (app.requiresReboot) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                                color: Colors.orange.withValues(alpha: 0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning,
                                  color: Colors.orange, size: 16),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Requires system reboot after installation',
                                  style: theme.textTheme.bodySmall
                                      ?.copyWith(color: Colors.orange[800]),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (!isInstalled)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _installApp(app);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Install'),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _terminalSubscription?.cancel();
    _terminalRefreshTimer?.cancel();
    _terminalVisibilityRefreshTimer?.cancel();
    _terminalScrollController.dispose();
    super.dispose();
  }
}
