import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'services/app_state.dart';
import 'services/mac_vendor_service.dart';
import 'shared/shared_service_registry.dart';
import 'screens/main_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize MAC vendor service
  await MacVendorService.initialize();

  // Initialize shared services
  SharedServiceRegistry().initialize();

  runApp(const RaspberryPiManagerApp());
}

class RaspberryPiManagerApp extends StatelessWidget {
  const RaspberryPiManagerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AppState(),
      child: MaterialApp(
        title: 'Jelly Pi',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          useMaterial3: true,
          brightness: Brightness.light,
          colorScheme: const ColorScheme.light(
            primary: Color(0xFF6366F1), // Purple accent from your design
            primaryContainer: Color(0xFFEEF2FF),
            secondary: Color(0xFF10B981), // Green for online status
            secondaryContainer: Color(0xFFD1FAE5),
            surface: Color(0xFFF8FAFC), // Light gray background
            surfaceContainerHighest: Color(0xFFFFFFFF), // White cards
            error: Color(0xFFEF4444),
            onPrimary: Color(0xFFFFFFFF),
            onSecondary: Color(0xFFFFFFFF),
            onSurface: Color(0xFF1F2937),
            onError: Color(0xFFFFFFFF),
            outline: Color(0xFFD1D5DB),
          ),
          scaffoldBackgroundColor: const Color(0xFFF8FAFC),
          // Use Google Fonts Inter for consistent typography across all devices
          textTheme: GoogleFonts.interTextTheme(),
          appBarTheme: AppBarTheme(
            elevation: 0,
            backgroundColor: const Color(0xFFFFFFFF),
            foregroundColor: const Color(0xFF1F2937),
            titleTextStyle: GoogleFonts.inter(
              color: const Color(0xFF1F2937),
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          cardTheme: CardTheme(
            elevation: 1,
            color: const Color(0xFFFFFFFF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        home: const MainScreen(),
      ),
    );
  }
}
