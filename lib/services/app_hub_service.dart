import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class AppHubService {
  final AppState appState;
  final RpiCommandService commandService;

  AppHubRepository? _repository;
  final Map<String, AppInstallationStatus> _installationStatus = {};

  // Terminal output persistence - now stored in AppState for global persistence
  bool _isAnyInstallationInProgress = false;
  bool _isCheckingStatus = false;
  bool _isApplyingFix = false;
  bool _isCancelled = false;

  // Track current operation details
  // String? _currentOperationAppId;  // Removed unused field
  // String? _currentOperationDeviceId;  // Removed unused field

  // Stream controller for real-time terminal output
  final StreamController<String> _terminalOutputController =
      StreamController<String>.broadcast();

  // Common error patterns and their fixes
  static const Map<String, String> _commonErrorFixes = {
    'dpkg was interrupted': 'sudo dpkg --configure -a',
    'Could not get lock /var/lib/dpkg/lock':
        'sudo rm /var/lib/dpkg/lock && sudo dpkg --configure -a',
    'Could not get lock /var/lib/apt/lists/lock':
        'sudo rm /var/lib/apt/lists/lock && sudo apt update',
    'Unable to acquire the dpkg frontend lock':
        'sudo killall apt apt-get && sudo dpkg --configure -a',
    'Package operation failed': 'sudo apt --fix-broken install',
    'Unmet dependencies': 'sudo apt --fix-broken install',
    'Hash Sum mismatch': 'sudo apt clean && sudo apt update',
  };

  AppHubService(this.appState, this.commandService);

  AppHubRepository? get repository => _repository;
  Map<String, AppInstallationStatus> get installationStatus =>
      _installationStatus;
  List<String> get terminalOutput => appState.appHubTerminalOutput;
  bool get isAnyInstallationInProgress => _isAnyInstallationInProgress;
  bool get isCheckingStatus => _isCheckingStatus;
  bool get isApplyingFix => _isApplyingFix;

  // Combined getter for any operation that should disable buttons
  bool get isAnyOperationInProgress =>
      _isAnyInstallationInProgress || _isCheckingStatus || _isApplyingFix;

  // Stream for real-time terminal output updates
  Stream<String> get terminalOutputStream => _terminalOutputController.stream;

  // Cancel current operation
  void cancelCurrentOperation() {
    if (_isAnyInstallationInProgress || _isApplyingFix) {
      _isCancelled = true;
      addTerminalOutput('');
      addTerminalOutput('🛑 Operation cancelled by user');
      addTerminalOutput('');
    }
  }

  // Check if operation was cancelled
  bool get isCancelled => _isCancelled;

  void addTerminalOutput(String output) {
    appState.addAppHubTerminalOutput(output);
  }

  void clearTerminalOutput() {
    appState.clearAppHubTerminalOutput();
  }

  void dispose() {
    _terminalOutputController.close();
  }

  // Check for common errors and offer fixes
  Future<bool> _checkForCommonErrors(
      String output, String deviceId, Function(String)? onOutput) async {
    // Don't check for errors if operation was cancelled
    if (_isCancelled) return false;

    for (final errorPattern in _commonErrorFixes.keys) {
      if (output.toLowerCase().contains(errorPattern.toLowerCase())) {
        final fixCommand = _commonErrorFixes[errorPattern]!;

        debugPrint('Error detected: $errorPattern');
        onOutput?.call('│  ⚠️  Error detected: $errorPattern');

        // Show error dialog and ask user if they want to apply the fix
        final shouldFix = await _showErrorFixDialog(errorPattern, fixCommand);

        if (shouldFix) {
          _isApplyingFix = true;

          try {
            onOutput?.call('│');
            onOutput?.call('│  🔧 Applying automatic fix...');
            onOutput?.call('│  \$ $fixCommand');
            onOutput?.call('│');

            final result = await commandService.executeLongRunningCommand(
              deviceId,
              fixCommand,
              progressTitle: 'Applying fix',
              onOutput: (fixOutput) {
                final lines = fixOutput.split('\n');
                for (final line in lines) {
                  if (line.trim().isNotEmpty) {
                    onOutput?.call('│  │ $line');
                  }
                }
              },
              timeout: const Duration(minutes: 5),
            );

            if (result.success) {
              onOutput?.call('│  │');
              onOutput?.call('│  └─ ✅ Fix applied successfully');
              return true;
            } else {
              onOutput?.call('│  │');
              onOutput?.call('│  └─ ❌ Fix failed: ${result.stderr}');
              return false;
            }
          } finally {
            _isApplyingFix = false;
          }
        } else {
          onOutput?.call('│  ❌ User declined to apply fix');
        }
        break;
      }
    }
    return false;
  }

  // Show error fix dialog (placeholder - will be implemented in UI)
  Future<bool> _showErrorFixDialog(String error, String fix) async {
    // This will be called from the UI layer
    return appState.showErrorFixDialog?.call(error, fix) ?? false;
  }

  /// Load the app repository from assets
  Future<void> loadRepository([String? deviceOS]) async {
    debugPrint('Starting App Hub repository loading...');
    try {
      // Try to load default repository based on device OS
      String? repositoryData = await _loadDefaultRepositoryForOS(deviceOS);

      // Fallback to first available repository if no default found
      repositoryData ??= await _loadRepositoryFromFileSystem();

      // Fallback to asset bundle if file system fails
      if (repositoryData == null) {
        try {
          debugPrint('Attempting to load repository from asset bundle...');
          repositoryData =
              await rootBundle.loadString('assets/app_hub/repository.json');
          debugPrint('Loaded App Hub repository from asset bundle');
        } catch (e) {
          debugPrint('Could not load repository from assets: $e');
          throw Exception(
              'Failed to load App Hub repository from both file system and assets');
        }
      } else {
        debugPrint('Loaded App Hub repository from file system');
      }

      debugPrint('Parsing JSON data...');
      final jsonData = jsonDecode(repositoryData) as Map<String, dynamic>;
      _repository = AppHubRepository.fromJson(jsonData);
      debugPrint(
          'Successfully loaded App Hub repository with ${_repository!.applications.length} applications');
      debugPrint('Repository name: ${_repository!.repository.name}');
      debugPrint('Categories: ${_repository!.categories.length}');
    } catch (e) {
      debugPrint('Error loading app repository: $e');
      rethrow;
    }
  }

  /// Load default repository based on device OS
  Future<String?> _loadDefaultRepositoryForOS(String? deviceOS) async {
    if (deviceOS == null) return null;

    try {
      final packageManager = _getPackageManagerForOS(deviceOS);
      final defaultRepoPath = await _getDefaultRepositoryPath(packageManager);

      if (defaultRepoPath != null) {
        final file = File(defaultRepoPath);
        if (await file.exists()) {
          debugPrint(
              'Loading default $packageManager repository: $defaultRepoPath');
          return await file.readAsString();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error loading default repository for OS $deviceOS: $e');
      return null;
    }
  }

  /// Get package manager type based on OS
  String _getPackageManagerForOS(String os) {
    final osLower = os.toLowerCase();

    if (osLower.contains('ubuntu') ||
        osLower.contains('debian') ||
        osLower.contains('raspbian') ||
        osLower.contains('raspberry pi os')) {
      return 'apt';
    } else if (osLower.contains('arch') ||
        osLower.contains('manjaro') ||
        osLower.contains('endeavour')) {
      return 'pacman';
    } else if (osLower.contains('fedora') ||
        osLower.contains('centos') ||
        osLower.contains('rhel') ||
        osLower.contains('red hat')) {
      return 'rpm';
    }

    // Default to apt for unknown Linux distributions
    return 'apt';
  }

  /// Load repository from file system (for development)
  Future<String?> _loadRepositoryFromFileSystem() async {
    try {
      // Get the project root directory
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }

      // Check for repositories in the new location
      final repositoriesDir = Directory('$projectRoot/assets/repositories');
      if (await repositoriesDir.exists()) {
        final files = await repositoriesDir
            .list()
            .where((entity) => entity is File && entity.path.endsWith('.json'))
            .toList();

        if (files.isNotEmpty) {
          // Load the first available repository file
          final firstRepo = files.first as File;
          debugPrint('Loading repository from: ${firstRepo.path}');
          return await firstRepo.readAsString();
        }
      }

      // Fallback to old location for backward compatibility
      final repositoryFile =
          File('$projectRoot/assets/app_hub/repository.json');
      if (await repositoryFile.exists()) {
        debugPrint(
            'Loading repository from legacy location: ${repositoryFile.path}');
        return await repositoryFile.readAsString();
      }

      return null;
    } catch (e) {
      debugPrint('Error loading repository from file system: $e');
      return null;
    }
  }

  /// Check installation status for all apps
  Future<void> checkInstallationStatus(String deviceId) async {
    if (_repository == null) return;

    // Don't check status if any operation is in progress locally
    if (_isAnyInstallationInProgress || _isApplyingFix) {
      debugPrint('Skipping status check - local operation in progress');
      return;
    }

    // Don't check status if apt is running on the remote device
    if (appState.isPackageOperationInProgress(deviceId)) {
      debugPrint('Skipping status check - apt operation in progress on device');
      return;
    }

    _isCheckingStatus = true;
    _installationStatus.clear();

    try {
      for (final app in _repository!.applications) {
        _installationStatus[app.id] = AppInstallationStatus.checking;

        try {
          final result = await commandService.executeCommand(
            deviceId,
            app.checkInstalled,
            showProgress: false,
          );

          _installationStatus[app.id] = result.success
              ? AppInstallationStatus.installed
              : AppInstallationStatus.notInstalled;
        } catch (e) {
          _installationStatus[app.id] = AppInstallationStatus.unknown;
        }
      }
    } finally {
      _isCheckingStatus = false;
    }
  }

  /// Install an application
  Future<bool> installApplication(
      String deviceId, AppHubApplication app) async {
    return installApplicationWithOutput(deviceId, app);
  }

  /// Install an application with terminal output
  Future<bool> installApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput, Function(bool)? onRebootRequired}) async {
    // Check if any operation is already in progress
    if (_isAnyInstallationInProgress || _isCheckingStatus || _isApplyingFix) {
      onOutput?.call(
          '❌ Another operation is already in progress. Please wait for it to complete.');
      return false;
    }

    // Check if package management is already running on the device
    if (appState.isPackageOperationInProgress(deviceId)) {
      onOutput?.call(
          '❌ Package management operation is already running on this device.');
      onOutput?.call(
          '   This could be from the terminal, system updates, or direct Pi access.');
      onOutput?.call(
          '   Please wait for the current operation to complete and try again.');
      return false;
    }

    // Clear terminal output at start of new operation
    clearTerminalOutput();

    _isAnyInstallationInProgress = true;
    _isCancelled = false; // Reset cancellation flag
    _installationStatus[app.id] = AppInstallationStatus.installing;

    try {
      final startMessage = '╭─ Starting installation of ${app.name}';
      onOutput?.call(startMessage);

      final separator = '│';
      onOutput?.call(separator);

      // Check dependencies first
      if (app.dependencies.isNotEmpty) {
        onOutput?.call('├─ Checking dependencies...');
        for (final dep in app.dependencies) {
          final depApp = _repository?.applications.firstWhere(
            (a) => a.id == dep,
            orElse: () => throw Exception('Dependency $dep not found'),
          );

          if (depApp != null) {
            final depStatus = _installationStatus[dep];
            if (depStatus != AppInstallationStatus.installed) {
              onOutput?.call('│  ├─ Installing dependency: $dep');
              // Install dependency first
              final depInstalled = await installApplicationWithOutput(
                  deviceId, depApp,
                  onOutput: onOutput, onRebootRequired: onRebootRequired);
              if (!depInstalled) {
                throw Exception('Failed to install dependency: $dep');
              }
              onOutput?.call('│  └─ Dependency $dep installed successfully');
            } else {
              onOutput?.call('│  ✓ Dependency $dep already installed');
            }
          }
        }
        onOutput?.call('│');
      }

      // Execute installation commands
      onOutput?.call('├─ Executing installation commands...');
      for (int i = 0; i < app.installCommands.length; i++) {
        // Check for cancellation
        if (_isCancelled) {
          onOutput?.call('│');
          onOutput?.call('╰─ 🛑 Installation cancelled by user');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        }

        final command = app.installCommands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${app.installCommands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Installing ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 30),
        );

        // Check for common errors in the command output after completion
        if (!result.success) {
          final fullOutput = '${result.stdout}\n${result.stderr}';
          await _checkForCommonErrors(fullOutput, deviceId, onOutput);
        }

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Installation failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Verify installation
      onOutput?.call('│');
      onOutput?.call('├─ Verifying installation...');
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (verifyResult.success) {
        onOutput?.call('│  ✅ Installation verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} installed successfully');

        // Check if reboot is required
        if (app.requiresReboot) {
          onOutput?.call('');
          onOutput?.call(
              '⚠️  This application requires a system reboot to function properly.');
          onRebootRequired?.call(true);
        }

        _installationStatus[app.id] = AppInstallationStatus.installed;
        return true;
      } else {
        final failMessage = '│  ❌ Installation verification failed';
        onOutput?.call(failMessage);

        final endMessage = '╰─ ❌ Installation verification failed';
        onOutput?.call('│');
        onOutput?.call(endMessage);

        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      final errorMessage = '╰─ ❌ Installation failed: $e';
      onOutput?.call('│');
      onOutput?.call(errorMessage);

      debugPrint('Error installing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Remove an application
  Future<bool> removeApplication(String deviceId, AppHubApplication app) async {
    return removeApplicationWithOutput(deviceId, app);
  }

  /// Remove an application with terminal output
  Future<bool> removeApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput,
      Function(Map<String, dynamic>)? onAutoRemoveDetected}) async {
    // Check if any operation is already in progress
    if (_isAnyInstallationInProgress || _isCheckingStatus || _isApplyingFix) {
      onOutput?.call(
          '❌ Another operation is already in progress. Please wait for it to complete.');
      return false;
    }

    // Check if package management is already running on the device
    if (appState.isPackageOperationInProgress(deviceId)) {
      onOutput?.call(
          '❌ Package management operation is already running on this device.');
      onOutput?.call(
          '   This could be from the terminal, system updates, or direct Pi access.');
      onOutput?.call(
          '   Please wait for the current operation to complete and try again.');
      return false;
    }

    // Clear terminal output at start of new operation
    clearTerminalOutput();

    _isAnyInstallationInProgress = true;
    _isCancelled = false; // Reset cancellation flag
    _installationStatus[app.id] = AppInstallationStatus.removing;

    try {
      final startMessage = '╭─ Starting removal of ${app.name}';
      onOutput?.call(startMessage);

      final separator = '│';
      onOutput?.call(separator);

      // Execute removal commands
      onOutput?.call('├─ Executing removal commands...');
      for (int i = 0; i < app.removeCommands.length; i++) {
        // Check for cancellation before each command
        if (_isCancelled) {
          onOutput?.call('│');
          onOutput?.call('╰─ 🛑 Removal cancelled by user');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        }

        final command = app.removeCommands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${app.removeCommands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Removing ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 15),
          cancellationToken: () => _isCancelled, // Pass cancellation token
        );

        // Check for common errors in the command output after completion
        if (!result.success) {
          final fullOutput = '${result.stdout}\n${result.stderr}';
          await _checkForCommonErrors(fullOutput, deviceId, onOutput);
        }

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Removal failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Check for autoremove suggestions first (before verification)
      // This ensures we check for orphaned packages even if verification has issues
      await _checkForAutoRemove(deviceId, onOutput, onAutoRemoveDetected);

      // Verify removal
      onOutput?.call('│');
      onOutput?.call('├─ Verifying removal...');

      // Try verification, but don't fail the entire removal if verification has issues
      bool verificationPassed = false;
      try {
        final verifyResult = await commandService.executeCommand(
          deviceId,
          app.checkInstalled,
          showProgress: false,
        );

        // When a package is successfully removed, the check command should FAIL (return non-zero exit code)
        // because the package should no longer be found/installed
        verificationPassed = !verifyResult.success;
      } catch (e) {
        // If verification command fails to run, assume removal was successful
        // since the removal commands completed without error
        verificationPassed = true;
        onOutput?.call(
            '│  ⚠️  Verification command failed, assuming removal successful');
      }

      if (verificationPassed) {
        onOutput?.call('│  ✅ Removal verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} removed successfully');

        _installationStatus[app.id] = AppInstallationStatus.notInstalled;
        return true;
      } else {
        onOutput?.call(
            '│  ⚠️  Removal verification inconclusive, but removal commands completed');
        onOutput?.call('│');
        onOutput?.call(
            '╰─ ✅ ${app.name} removal completed (verification inconclusive)');

        // Still mark as successful since removal commands completed
        _installationStatus[app.id] = AppInstallationStatus.notInstalled;
        return true;
      }
    } catch (e) {
      onOutput?.call('│');
      onOutput?.call('╰─ ❌ Removal failed: $e');
      debugPrint('Error removing ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Update an application
  Future<bool> updateApplication(String deviceId, AppHubApplication app) async {
    return updateApplicationWithOutput(deviceId, app);
  }

  /// Update an application with terminal output
  Future<bool> updateApplicationWithOutput(
      String deviceId, AppHubApplication app,
      {Function(String)? onOutput, Function(bool)? onRebootRequired}) async {
    // Check if any operation is already in progress
    if (_isAnyInstallationInProgress || _isCheckingStatus || _isApplyingFix) {
      onOutput?.call(
          '❌ Another operation is already in progress. Please wait for it to complete.');
      return false;
    }

    // Check if package management is already running on the device
    if (appState.isPackageOperationInProgress(deviceId)) {
      onOutput?.call(
          '❌ Package management operation is already running on this device.');
      onOutput?.call(
          '   This could be from the terminal, system updates, or direct Pi access.');
      onOutput?.call(
          '   Please wait for the current operation to complete and try again.');
      return false;
    }

    // Clear terminal output at start of new operation
    clearTerminalOutput();

    _isAnyInstallationInProgress = true;
    _isCancelled = false; // Reset cancellation flag
    _installationStatus[app.id] = AppInstallationStatus.updating;

    try {
      final startMessage = '╭─ Starting update of ${app.name}';
      onOutput?.call(startMessage);

      final separator = '│';
      onOutput?.call(separator);

      // Use update commands if available, otherwise fall back to install commands
      final commands = app.updateCommands ?? app.installCommands;

      // Execute update commands
      onOutput?.call('├─ Executing update commands...');
      for (int i = 0; i < commands.length; i++) {
        final command = commands[i];
        onOutput?.call('│');
        onOutput?.call('│  ┌─ Command ${i + 1}/${commands.length}');
        onOutput?.call('│  │ \$ $command');
        onOutput?.call('│  │');

        final result = await commandService.executeLongRunningCommand(
          deviceId,
          command,
          progressTitle: 'Updating ${app.name}',
          onOutput: (output) {
            // Prefix each line of command output with terminal formatting
            final lines = output.split('\n');
            for (final line in lines) {
              if (line.trim().isNotEmpty) {
                onOutput?.call('│  │ $line');
              }
            }
          },
          timeout: const Duration(minutes: 30),
        );

        // Check for common errors in the command output after completion
        if (!result.success) {
          final fullOutput = '${result.stdout}\n${result.stderr}';
          await _checkForCommonErrors(fullOutput, deviceId, onOutput);
        }

        if (!result.success) {
          onOutput?.call('│  │');
          onOutput?.call(
              '│  └─ ❌ Command failed with exit code: ${result.exitCode}');
          if (result.stderr.isNotEmpty) {
            onOutput?.call('│     Error: ${result.stderr}');
          }
          onOutput?.call('│');
          onOutput?.call('╰─ ❌ Update failed');
          _installationStatus[app.id] = AppInstallationStatus.failed;
          return false;
        } else {
          onOutput?.call('│  │');
          onOutput?.call('│  └─ ✅ Command completed successfully');
        }
      }

      // Verify installation
      onOutput?.call('│');
      onOutput?.call('├─ Verifying update...');
      final verifyResult = await commandService.executeCommand(
        deviceId,
        app.checkInstalled,
        showProgress: false,
      );

      if (verifyResult.success) {
        onOutput?.call('│  ✅ Update verified successfully');
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ ${app.name} updated successfully');

        // Check if reboot is required
        if (app.requiresReboot) {
          onOutput?.call('');
          onOutput?.call(
              '⚠️  This application requires a system reboot to function properly.');
          onRebootRequired?.call(true);
        }

        _installationStatus[app.id] = AppInstallationStatus.installed;
        return true;
      } else {
        onOutput?.call('│  ❌ Update verification failed');
        onOutput?.call('│');
        onOutput?.call('╰─ ❌ Update verification failed');
        _installationStatus[app.id] = AppInstallationStatus.failed;
        return false;
      }
    } catch (e) {
      onOutput?.call('│');
      onOutput?.call('╰─ ❌ Update failed: $e');
      debugPrint('Error updating ${app.name}: $e');
      _installationStatus[app.id] = AppInstallationStatus.failed;
      return false;
    } finally {
      _isAnyInstallationInProgress = false;
    }
  }

  /// Get applications by category
  List<AppHubApplication> getApplicationsByCategory(String categoryId) {
    if (_repository == null) return [];
    return _repository!.applications
        .where((app) => app.category == categoryId)
        .toList();
  }

  /// Check for packages that can be auto-removed
  Future<void> _checkForAutoRemove(String deviceId, Function(String)? onOutput,
      Function(Map<String, dynamic>)? onAutoRemoveDetected) async {
    try {
      onOutput?.call('│');
      onOutput?.call('├─ Checking for orphaned packages...');

      // Check for packages that can be auto-removed using the correct command
      final result = await commandService.executeCommand(
        deviceId,
        'sudo apt autoremove --dry-run 2>/dev/null',
        showProgress: false,
      );

      if (result.success) {
        final output = result.stdout.trim();

        // Look for the "The following packages will be REMOVED:" pattern
        final hasRemovablePackages =
            output.contains('The following packages will be REMOVED:');

        if (hasRemovablePackages) {
          // Use grep approach which is more reliable for extracting package names
          final packagesResult = await commandService.executeCommand(
            deviceId,
            'sudo apt autoremove --dry-run 2>/dev/null | grep "^Remv" | cut -d" " -f2',
            showProgress: false,
          );

          List<String> packages = [];
          if (packagesResult.success) {
            packages = packagesResult.stdout
                .trim()
                .split('\n')
                .where((pkg) => pkg.isNotEmpty)
                .toList();
          }

          // If grep didn't work, try parsing the output manually
          if (packages.isEmpty) {
            final lines = output.split('\n');
            bool foundPackagesList = false;

            for (final line in lines) {
              if (line.contains('The following packages will be REMOVED:')) {
                foundPackagesList = true;
                continue;
              }

              if (foundPackagesList) {
                // Stop when we hit an empty line or end of package list
                if (line.trim().isEmpty ||
                    line.contains('Need to get') ||
                    line.contains('After this operation')) {
                  break;
                }

                // Extract package names from lines like "  adwaita-icon-theme at-spi2-common"
                final trimmed = line.trim();
                if (trimmed.isNotEmpty) {
                  // Split by spaces and take each package name
                  final packageNames = trimmed.split(RegExp(r'\s+'));
                  for (final pkg in packageNames) {
                    if (pkg.isNotEmpty && !pkg.startsWith('*')) {
                      packages.add(pkg);
                    }
                  }
                }
              }
            }
          }

          // Extract disk space that will be freed
          String diskSpace = '';
          final spaceMatch =
              RegExp(r'After this operation, (.+?) of disk space will be freed')
                  .firstMatch(output);
          if (spaceMatch != null) {
            diskSpace = spaceMatch.group(1) ?? '';
          }

          if (packages.isNotEmpty) {
            onOutput?.call('│');
            onOutput?.call(
                '│  📦 Found ${packages.length} packages that are no longer required');
            if (diskSpace.isNotEmpty) {
              onOutput?.call('│     💾 Will free $diskSpace of disk space');
            }
            for (final pkg in packages.take(5)) {
              onOutput?.call('│     • $pkg');
            }
            if (packages.length > 5) {
              onOutput?.call('│     • ... and ${packages.length - 5} more');
            }
            onOutput?.call('│');

            // Trigger the callback to show the dialog with package info
            onAutoRemoveDetected?.call({
              'packages': packages,
              'diskSpace': diskSpace,
            });
          } else {
            onOutput?.call('│');
            onOutput?.call('│  ✅ No orphaned packages found');
            onOutput?.call('│     System is clean!');
            onOutput?.call('│');
          }
        } else {
          onOutput?.call('│');
          onOutput?.call('│  ✅ No orphaned packages found');
          onOutput?.call('│     System is clean!');
          onOutput?.call('│');
        }
      }
    } catch (e) {
      debugPrint('Error checking for auto-remove: $e');
    }
  }

  /// Check for orphaned packages manually (public method)
  Future<void> checkForAutoRemove(String deviceId,
      {Function(String)? onOutput,
      Function(Map<String, dynamic>)? onAutoRemoveDetected}) async {
    await _checkForAutoRemove(deviceId, onOutput, onAutoRemoveDetected);
  }

  /// Execute auto-remove command
  Future<bool> executeAutoRemove(String deviceId,
      {Function(String)? onOutput}) async {
    try {
      onOutput?.call('');
      onOutput?.call('╭─ Running apt autoremove');
      onOutput?.call('│');

      final result = await commandService.executeLongRunningCommand(
        deviceId,
        'sudo apt autoremove -y',
        progressTitle: 'Auto-removing packages',
        onOutput: (output) {
          final lines = output.split('\n');
          for (final line in lines) {
            if (line.trim().isNotEmpty) {
              onOutput?.call('│  $line');
            }
          }
        },
        timeout: const Duration(minutes: 10),
      );

      if (result.success) {
        onOutput?.call('│');
        onOutput?.call('╰─ ✅ Auto-remove completed successfully');
        return true;
      } else {
        onOutput?.call('│');
        onOutput?.call('╰─ ❌ Auto-remove failed');
        return false;
      }
    } catch (e) {
      onOutput?.call('│');
      onOutput?.call('╰─ ❌ Auto-remove failed: $e');
      return false;
    }
  }

  /// Search applications
  List<AppHubApplication> searchApplications(String query) {
    if (_repository == null) return [];
    final lowerQuery = query.toLowerCase();
    return _repository!.applications.where((app) {
      return app.name.toLowerCase().contains(lowerQuery) ||
          app.description.toLowerCase().contains(lowerQuery) ||
          app.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get default repository path for a package manager
  Future<String?> _getDefaultRepositoryPath(String packageManager) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('default_repository_$packageManager');
    } catch (e) {
      debugPrint(
          'Error getting default repository path for $packageManager: $e');
      return null;
    }
  }

  /// Set default repository for a package manager
  Future<void> setDefaultRepository(
      String packageManager, String repositoryPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'default_repository_$packageManager', repositoryPath);
      debugPrint('Set default $packageManager repository to: $repositoryPath');
    } catch (e) {
      debugPrint('Error setting default repository for $packageManager: $e');
    }
  }

  /// Get all available repositories
  Future<List<File>> getAvailableRepositories() async {
    try {
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }

      final repositoriesDir = Directory('$projectRoot/assets/repositories');
      if (await repositoriesDir.exists()) {
        return await repositoriesDir
            .list()
            .where((entity) => entity is File && entity.path.endsWith('.json'))
            .cast<File>()
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting available repositories: $e');
      return [];
    }
  }

  /// Load repository from specific file
  Future<void> loadRepositoryFromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final content = await file.readAsString();
        final jsonData = jsonDecode(content) as Map<String, dynamic>;
        _repository = AppHubRepository.fromJson(jsonData);
        debugPrint('Loaded repository from: $filePath');
        debugPrint('Repository name: ${_repository!.repository.name}');
      }
    } catch (e) {
      debugPrint('Error loading repository from file $filePath: $e');
      rethrow;
    }
  }

  /// Check if repository is compatible with device OS
  bool isRepositoryCompatible(AppHubRepository repository, String deviceOS) {
    final requiredPackageManager = _getPackageManagerForOS(deviceOS);
    return repository.repository.type.contains(requiredPackageManager);
  }
}

enum AppInstallationStatus {
  unknown,
  checking,
  notInstalled,
  installed,
  installing,
  removing,
  failed,
  updateAvailable,
  updating,
}

class AppHubRepository {
  final RepositoryInfo repository;
  final List<AppCategory> categories;
  final List<AppHubApplication> applications;

  AppHubRepository({
    required this.repository,
    required this.categories,
    required this.applications,
  });

  factory AppHubRepository.fromJson(Map<String, dynamic> json) {
    return AppHubRepository(
      repository: RepositoryInfo.fromJson(json['repository']),
      categories: (json['categories'] as List)
          .map((cat) => AppCategory.fromJson(cat))
          .toList(),
      applications: (json['applications'] as List)
          .map((app) => AppHubApplication.fromJson(app))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'repository': repository.toJson(),
      'categories': categories.map((cat) => cat.toJson()).toList(),
      'applications': applications.map((app) => app.toJson()).toList(),
    };
  }
}

class RepositoryInfo {
  final String name;
  final String version;
  final String description;
  final String source;
  final List<String> type;
  final DateTime lastUpdated;

  RepositoryInfo({
    required this.name,
    required this.version,
    required this.description,
    required this.source,
    required this.type,
    required this.lastUpdated,
  });

  factory RepositoryInfo.fromJson(Map<String, dynamic> json) {
    return RepositoryInfo(
      name: json['name'],
      version: json['version'],
      description: json['description'],
      source: json['source'] ?? 'Unknown',
      type: List<String>.from(json['type'] ?? ['deb']),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'version': version,
      'description': description,
      'source': source,
      'type': type,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

class AppCategory {
  final String id;
  final String name;
  final String description;
  final String icon;

  AppCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
  });

  factory AppCategory.fromJson(Map<String, dynamic> json) {
    return AppCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
    };
  }
}

class AppHubApplication {
  final String id;
  final String name;
  final String description;
  final String version;
  final String category;
  final String icon;
  final String? iconPath; // For custom icons
  final String size;
  final String installType;
  final List<String> installCommands;
  final List<String> removeCommands;
  final String checkInstalled;
  final String? checkVersion;
  final List<String>? updateCommands;
  final List<String> tags;
  final bool requiresReboot;
  final bool piOnly;
  final List<String> dependencies;

  AppHubApplication({
    required this.id,
    required this.name,
    required this.description,
    required this.version,
    required this.category,
    required this.icon,
    this.iconPath,
    required this.size,
    required this.installType,
    required this.installCommands,
    required this.removeCommands,
    required this.checkInstalled,
    this.checkVersion,
    this.updateCommands,
    required this.tags,
    required this.requiresReboot,
    required this.piOnly,
    this.dependencies = const [],
  });

  factory AppHubApplication.fromJson(Map<String, dynamic> json) {
    return AppHubApplication(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      version: json['version'],
      category: json['category'],
      icon: json['icon'],
      iconPath: json['icon_path'],
      size: json['size'],
      installType: json['install_type'],
      installCommands: List<String>.from(json['install_commands']),
      removeCommands: List<String>.from(json['remove_commands']),
      checkInstalled: json['check_installed'],
      checkVersion: json['check_version'],
      updateCommands: json['update_commands'] != null
          ? List<String>.from(json['update_commands'])
          : null,
      tags: List<String>.from(json['tags']),
      requiresReboot: json['requires_reboot'] ?? false,
      piOnly: json['pi_only'] ?? false,
      dependencies: List<String>.from(json['dependencies'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'version': version,
      'category': category,
      'icon': icon,
      if (iconPath != null) 'icon_path': iconPath,
      'size': size,
      'install_type': installType,
      'install_commands': installCommands,
      'remove_commands': removeCommands,
      'check_installed': checkInstalled,
      if (checkVersion != null) 'check_version': checkVersion,
      if (updateCommands != null) 'update_commands': updateCommands,
      'tags': tags,
      'requires_reboot': requiresReboot,
      'pi_only': piOnly,
      if (dependencies.isNotEmpty) 'dependencies': dependencies,
    };
  }
}
