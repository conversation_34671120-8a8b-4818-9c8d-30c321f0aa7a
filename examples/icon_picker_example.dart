import 'package:flutter/material.dart';
import '../lib/shared/shared_service_registry.dart';

/// Example demonstrating how to use the Icon Picker Service
/// This shows how to integrate icon selection into your custom apps
class IconPickerExample extends StatefulWidget {
  const IconPickerExample({super.key});

  @override
  State<IconPickerExample> createState() => _IconPickerExampleState();
}

class _IconPickerExampleState extends State<IconPickerExample> {
  String _selectedIcon = 'apps';
  String? _selectedIconPath;
  List<Map<String, dynamic>> _iconHistory = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Icon Picker Example'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Selection Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        // Display the selected icon using shared service
                        Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SharedServiceRegistry().buildIconWidget(
                            _selectedIcon,
                            _selectedIconPath,
                            size: 32,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Icon: $_selectedIcon'),
                              if (_selectedIconPath != null)
                                Text('Custom Path: $_selectedIconPath'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _showIconPicker,
                      icon: const Icon(Icons.palette),
                      label: const Text('Change Icon'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Usage Examples Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Different Sizes',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        _buildIconExample(16, 'Small (16px)'),
                        const SizedBox(width: 16),
                        _buildIconExample(24, 'Medium (24px)'),
                        const SizedBox(width: 16),
                        _buildIconExample(32, 'Large (32px)'),
                        const SizedBox(width: 16),
                        _buildIconExample(48, 'XL (48px)'),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // History Section
            if (_iconHistory.isNotEmpty) ...[
              Text(
                'Selection History',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _iconHistory.length,
                  itemBuilder: (context, index) {
                    final item = _iconHistory[index];
                    return ListTile(
                      leading: SharedServiceRegistry().buildIconWidget(
                        item['iconName'],
                        item['iconPath'],
                        size: 24,
                      ),
                      title: Text(item['iconName']),
                      subtitle: item['iconPath'] != null
                          ? Text('Custom: ${item['iconPath']}')
                          : const Text('Material Icon'),
                      trailing: IconButton(
                        icon: const Icon(Icons.restore),
                        onPressed: () => _restoreIcon(item),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIconExample(double size, String label) {
    return Column(
      children: [
        SharedServiceRegistry().buildIconWidget(
          _selectedIcon,
          _selectedIconPath,
          size: size,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Future<void> _showIconPicker() async {
    // Use the shared icon picker service
    final result = await SharedServiceRegistry().showIconPicker(
      context,
      selectedIcon: _selectedIcon,
      selectedIconPath: _selectedIconPath,
    );

    if (result != null) {
      // Save current selection to history
      _iconHistory.insert(0, {
        'iconName': _selectedIcon,
        'iconPath': _selectedIconPath,
        'timestamp': DateTime.now(),
      });

      // Limit history to 10 items
      if (_iconHistory.length > 10) {
        _iconHistory.removeLast();
      }

      setState(() {
        _selectedIcon = result['iconName'] ?? 'apps';
        _selectedIconPath = result['iconPath'];
      });
    }
  }

  void _restoreIcon(Map<String, dynamic> item) {
    setState(() {
      _selectedIcon = item['iconName'];
      _selectedIconPath = item['iconPath'];
    });
  }
}

/// Example of using icon picker in a form
class IconPickerFormExample extends StatefulWidget {
  const IconPickerFormExample({super.key});

  @override
  State<IconPickerFormExample> createState() => _IconPickerFormExampleState();
}

class _IconPickerFormExampleState extends State<IconPickerFormExample> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  String _selectedIcon = 'apps';
  String? _selectedIconPath;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Icon Picker in Form')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Icon selection field
              InkWell(
                onTap: _showIconPicker,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Theme.of(context).colorScheme.outline),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      SharedServiceRegistry().buildIconWidget(
                        _selectedIcon,
                        _selectedIconPath,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(_selectedIcon),
                      ),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _submitForm,
                child: const Text('Submit'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showIconPicker() async {
    final result = await SharedServiceRegistry().showIconPicker(
      context,
      selectedIcon: _selectedIcon,
      selectedIconPath: _selectedIconPath,
    );

    if (result != null) {
      setState(() {
        _selectedIcon = result['iconName'] ?? 'apps';
        _selectedIconPath = result['iconPath'];
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Process form data
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Submitted: ${_nameController.text} with icon $_selectedIcon'),
        ),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
}
