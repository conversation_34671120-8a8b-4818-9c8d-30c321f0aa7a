import 'package:flutter/material.dart';
import '../lib/shared/shared_service_registry.dart';

/// Example demonstrating how to use the Dialog Service
/// This shows various types of dialogs available through shared functionality
class DialogServiceExample extends StatefulWidget {
  const DialogServiceExample({super.key});

  @override
  State<DialogServiceExample> createState() => _DialogServiceExampleState();
}

class _DialogServiceExampleState extends State<DialogServiceExample> {
  String _lastResult = 'No action taken';
  String _userInput = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dialog Service Example'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Result Display
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Last Result',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_lastResult),
                    if (_userInput.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text('User Input: $_userInput'),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Confirmation Dialogs
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Confirmation Dialogs',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _showBasicConfirmation,
                          child: const Text('Basic Confirmation'),
                        ),
                        ElevatedButton(
                          onPressed: _showDeleteConfirmation,
                          child: const Text('Delete Confirmation'),
                        ),
                        ElevatedButton(
                          onPressed: _showWarningConfirmation,
                          child: const Text('With Warning'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Information Dialogs
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Information Dialogs',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _showInfoDialog,
                          child: const Text('Info Dialog'),
                        ),
                        ElevatedButton(
                          onPressed: _showSuccessDialog,
                          child: const Text('Success Dialog'),
                        ),
                        ElevatedButton(
                          onPressed: _showErrorDialog,
                          child: const Text('Error Dialog'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Input Dialogs
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Input Dialogs',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _showTextInput,
                          child: const Text('Text Input'),
                        ),
                        ElevatedButton(
                          onPressed: _showMultilineInput,
                          child: const Text('Multiline Input'),
                        ),
                        ElevatedButton(
                          onPressed: _showValidatedInput,
                          child: const Text('Validated Input'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Loading Dialog
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loading Dialog',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _showLoadingDialog,
                      child: const Text('Show Loading'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Confirmation Dialog Examples
  Future<void> _showBasicConfirmation() async {
    final result = await SharedServiceRegistry.showConfirmation(
      context,
      title: 'Confirm Action',
      message: 'Are you sure you want to proceed with this action?',
    );

    setState(() {
      _lastResult = result ? 'User confirmed' : 'User cancelled';
    });
  }

  Future<void> _showDeleteConfirmation() async {
    final result = await SharedServiceRegistry.showDeleteConfirmation(
      context,
      itemName: 'Important File.txt',
      warning: 'This file contains important data that cannot be recovered.',
    );

    setState(() {
      _lastResult = result ? 'File would be deleted' : 'Delete cancelled';
    });
  }

  Future<void> _showWarningConfirmation() async {
    final result = await SharedServiceRegistry.showConfirmation(
      context,
      title: 'Dangerous Operation',
      message: 'This will permanently modify system settings.',
      confirmText: 'Proceed',
      confirmColor: Colors.orange,
      warning: 'This action may cause system instability.',
      icon: Icons.warning,
    );

    setState(() {
      _lastResult = result ? 'User proceeded with warning' : 'User cancelled dangerous operation';
    });
  }

  // Information Dialog Examples
  Future<void> _showInfoDialog() async {
    await SharedServiceRegistry.showInfo(
      context,
      title: 'Information',
      message: 'This is an informational message to help you understand the current state.',
      icon: Icons.info,
    );

    setState(() {
      _lastResult = 'Info dialog shown';
    });
  }

  Future<void> _showSuccessDialog() async {
    await SharedServiceRegistry.showSuccess(
      context,
      title: 'Success!',
      message: 'Your operation completed successfully.',
    );

    setState(() {
      _lastResult = 'Success dialog shown';
    });
  }

  Future<void> _showErrorDialog() async {
    await SharedServiceRegistry.showError(
      context,
      title: 'Error Occurred',
      message: 'Something went wrong while processing your request. Please try again.',
    );

    setState(() {
      _lastResult = 'Error dialog shown';
    });
  }

  // Input Dialog Examples
  Future<void> _showTextInput() async {
    final result = await SharedServiceRegistry.showTextInput(
      context,
      title: 'Enter Name',
      message: 'Please enter your name:',
      hintText: 'Your name here...',
    );

    setState(() {
      _lastResult = result != null ? 'Text input received' : 'Input cancelled';
      _userInput = result ?? '';
    });
  }

  Future<void> _showMultilineInput() async {
    final result = await SharedServiceRegistry.showTextInput(
      context,
      title: 'Enter Description',
      message: 'Please provide a detailed description:',
      hintText: 'Enter description...',
      maxLines: 3,
    );

    setState(() {
      _lastResult = result != null ? 'Multiline input received' : 'Input cancelled';
      _userInput = result ?? '';
    });
  }

  Future<void> _showValidatedInput() async {
    final result = await SharedServiceRegistry.showTextInput(
      context,
      title: 'Enter Email',
      message: 'Please enter a valid email address:',
      hintText: '<EMAIL>',
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Email is required';
        }
        if (!value.contains('@') || !value.contains('.')) {
          return 'Please enter a valid email address';
        }
        return null;
      },
    );

    setState(() {
      _lastResult = result != null ? 'Valid email received' : 'Input cancelled';
      _userInput = result ?? '';
    });
  }

  // Loading Dialog Example
  Future<void> _showLoadingDialog() async {
    // Show loading dialog
    SharedServiceRegistry.showLoading(
      context,
      title: 'Processing...',
      message: 'Please wait while we process your request.',
    );

    // Simulate some work
    await Future.delayed(const Duration(seconds: 3));

    // Hide loading dialog
    if (mounted) {
      SharedServiceRegistry.hideLoading(context);
      
      setState(() {
        _lastResult = 'Loading completed';
      });
    }
  }
}

/// Example showing dialog service in a real-world scenario
class DialogServiceRealWorldExample extends StatefulWidget {
  const DialogServiceRealWorldExample({super.key});

  @override
  State<DialogServiceRealWorldExample> createState() => _DialogServiceRealWorldExampleState();
}

class _DialogServiceRealWorldExampleState extends State<DialogServiceRealWorldExample> {
  List<String> _items = ['Item 1', 'Item 2', 'Item 3'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Real World Example'),
        actions: [
          IconButton(
            onPressed: _addItem,
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: ListView.builder(
        itemCount: _items.length,
        itemBuilder: (context, index) {
          return ListTile(
            title: Text(_items[index]),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _editItem(index),
                  icon: const Icon(Icons.edit),
                ),
                IconButton(
                  onPressed: () => _deleteItem(index),
                  icon: const Icon(Icons.delete),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _addItem() async {
    final name = await SharedServiceRegistry.showTextInput(
      context,
      title: 'Add Item',
      message: 'Enter the name for the new item:',
      hintText: 'Item name...',
    );

    if (name != null && name.isNotEmpty) {
      setState(() {
        _items.add(name);
      });

      await SharedServiceRegistry.showSuccess(
        context,
        title: 'Item Added',
        message: 'The item "$name" has been added successfully.',
      );
    }
  }

  Future<void> _editItem(int index) async {
    final newName = await SharedServiceRegistry.showTextInput(
      context,
      title: 'Edit Item',
      message: 'Enter the new name for this item:',
      initialValue: _items[index],
    );

    if (newName != null && newName.isNotEmpty) {
      setState(() {
        _items[index] = newName;
      });
    }
  }

  Future<void> _deleteItem(int index) async {
    final confirmed = await SharedServiceRegistry.showDeleteConfirmation(
      context,
      itemName: _items[index],
    );

    if (confirmed) {
      setState(() {
        _items.removeAt(index);
      });

      await SharedServiceRegistry.showSuccess(
        context,
        title: 'Item Deleted',
        message: 'The item has been deleted successfully.',
      );
    }
  }
}
