# Jelly Pi Shared Functionality Examples

This directory contains examples and documentation for using Jelly Pi's shared functionality system in custom user apps.

## Overview

Jelly Pi provides a comprehensive shared functionality system that allows developers to create custom apps without duplicating common code. The system includes:

- **Icon Picker Service** - Unified icon selection with Material icons and custom images
- **File Operations Service** - File picking, saving, and directory operations
- **Dialog Service** - Standardized dialogs for confirmations, alerts, and user input
- **Theme Service** - Consistent theming and color utilities
- **Progress Notifications** - Built-in progress indicators and notifications

## Getting Started

### 1. Import the Shared Service Registry

```dart
import '../shared/shared_service_registry.dart';
```

### 2. Initialize Services (Done automatically in main.dart)

The shared services are automatically initialized when the app starts. You can access them through the `SharedServiceRegistry` singleton.

## Available Services

### Icon Picker Service

Select Material icons or custom images with a unified interface.

**Features:**
- Material Design icon library
- Custom image selection
- Consistent UI across all apps
- Automatic fallback handling

**Example Usage:** See `examples/icon_picker_example.dart`

### File Operations Service

Handle file and directory operations with a consistent API.

**Features:**
- File picking (single/multiple)
- Image selection
- File saving
- Directory selection
- File existence checking
- Human-readable file sizes

**Example Usage:** See `examples/file_operations_example.dart`

### Dialog Service

Create standardized dialogs for user interactions.

**Features:**
- Confirmation dialogs
- Delete confirmations
- Info/Error/Success dialogs
- Text input dialogs
- Loading dialogs

**Example Usage:** See `examples/dialog_service_example.dart`

### Theme Service

Access consistent theming and color utilities.

**Features:**
- Status colors (online, offline, error, etc.)
- Action colors (install, update, remove)
- Card decorations
- Button styles
- Responsive utilities

**Example Usage:** See `examples/theme_service_example.dart`

## Creating a Custom App

### Basic App Structure

```dart
import 'package:flutter/material.dart';
import '../shared/shared_service_registry.dart';

class MyCustomApp extends StatefulWidget {
  const MyCustomApp({super.key});

  @override
  State<MyCustomApp> createState() => _MyCustomAppState();
}

class _MyCustomAppState extends State<MyCustomApp> {
  String _selectedIcon = 'apps';
  String? _selectedIconPath;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Custom App'),
      ),
      body: Column(
        children: [
          // Use shared icon picker
          ElevatedButton(
            onPressed: _showIconPicker,
            child: const Text('Pick Icon'),
          ),
          
          // Display selected icon using shared service
          SharedServiceRegistry().buildIconWidget(
            _selectedIcon,
            _selectedIconPath,
            size: 48,
          ),
        ],
      ),
    );
  }

  Future<void> _showIconPicker() async {
    final result = await SharedServiceRegistry().showIconPicker(
      context,
      selectedIcon: _selectedIcon,
      selectedIconPath: _selectedIconPath,
    );

    if (result != null) {
      setState(() {
        _selectedIcon = result['iconName'] ?? 'apps';
        _selectedIconPath = result['iconPath'];
      });
    }
  }
}
```

## Best Practices

1. **Always use shared services** instead of implementing your own versions
2. **Handle null returns** from async operations gracefully
3. **Use consistent theming** through the Theme Service
4. **Provide user feedback** with progress notifications and dialogs
5. **Follow the established patterns** shown in the examples

## File Structure

```
examples/
├── README.md                    # This file
├── icon_picker_example.dart     # Icon picker usage examples
├── file_operations_example.dart # File operations examples
├── dialog_service_example.dart  # Dialog service examples
├── theme_service_example.dart   # Theme service examples
└── complete_app_example.dart    # Full app example using all services
```

## Contributing

When adding new shared functionality:

1. Add the service to `lib/shared/`
2. Update `SharedServiceRegistry` to include the new service
3. Create examples in this directory
4. Update this README with documentation

## Support

For questions about using shared functionality, refer to the examples in this directory or examine how existing apps in the Jelly Pi codebase use these services.
