import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../lib/shared/shared_service_registry.dart';

/// Example demonstrating how to use the File Operations Service
/// This shows various file and directory operations using shared functionality
class FileOperationsExample extends StatefulWidget {
  const FileOperationsExample({super.key});

  @override
  State<FileOperationsExample> createState() => _FileOperationsExampleState();
}

class _FileOperationsExampleState extends State<FileOperationsExample> {
  String? _selectedFile;
  String? _selectedDirectory;
  List<String> _selectedFiles = [];
  String? _selectedImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('File Operations Example'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Single File Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Single File Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_selectedFile != null)
                      Text('Selected: $_selectedFile')
                    else
                      const Text('No file selected'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _pickAnyFile,
                          child: const Text('Pick Any File'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _pickTextFile,
                          child: const Text('Pick Text File'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Multiple Files Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Multiple Files Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Selected ${_selectedFiles.length} files'),
                    if (_selectedFiles.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        height: 100,
                        decoration: BoxDecoration(
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListView.builder(
                          itemCount: _selectedFiles.length,
                          itemBuilder: (context, index) {
                            return ListTile(
                              dense: true,
                              title: Text(_selectedFiles[index].split('/').last),
                              subtitle: Text(_selectedFiles[index]),
                            );
                          },
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _pickMultipleFiles,
                      child: const Text('Pick Multiple Files'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Image Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Image Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_selectedImage != null) ...[
                      Text('Selected: ${_selectedImage!.split('/').last}'),
                      const SizedBox(height: 8),
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            _selectedImage!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(Icons.image, size: 50);
                            },
                          ),
                        ),
                      ),
                    ] else
                      const Text('No image selected'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _pickImage,
                      child: const Text('Pick Image'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Directory Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Directory Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_selectedDirectory != null)
                      Text('Selected: $_selectedDirectory')
                    else
                      const Text('No directory selected'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _pickDirectory,
                      child: const Text('Pick Directory'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // File Operations
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'File Operations',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _saveTextFile,
                          child: const Text('Save Text File'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _saveJsonFile,
                          child: const Text('Save JSON File'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Single file selection examples
  Future<void> _pickAnyFile() async {
    final result = await SharedServiceRegistry().pickFile(
      dialogTitle: 'Select any file',
    );

    if (result != null) {
      setState(() {
        _selectedFile = result;
      });
    }
  }

  Future<void> _pickTextFile() async {
    final result = await SharedServiceRegistry().pickFile(
      dialogTitle: 'Select a text file',
      type: FileType.custom,
      allowedExtensions: ['txt', 'md', 'log'],
    );

    if (result != null) {
      setState(() {
        _selectedFile = result;
      });
    }
  }

  // Multiple files selection
  Future<void> _pickMultipleFiles() async {
    final registry = SharedServiceRegistry();
    final result = await registry.fileOperations.pickFiles(
      dialogTitle: 'Select multiple files',
    );

    setState(() {
      _selectedFiles = result;
    });
  }

  // Image selection
  Future<void> _pickImage() async {
    final result = await SharedServiceRegistry().pickImage(
      dialogTitle: 'Select an image',
    );

    if (result != null) {
      setState(() {
        _selectedImage = result;
      });
    }
  }

  // Directory selection
  Future<void> _pickDirectory() async {
    final result = await SharedServiceRegistry().pickDirectory(
      dialogTitle: 'Select a directory',
    );

    if (result != null) {
      setState(() {
        _selectedDirectory = result;
      });
    }
  }

  // File saving examples
  Future<void> _saveTextFile() async {
    final result = await SharedServiceRegistry().saveFile(
      dialogTitle: 'Save text file',
      fileName: 'example.txt',
      type: FileType.custom,
      allowedExtensions: ['txt'],
    );

    if (result != null) {
      // Here you would write your content to the file
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Would save to: $result')),
      );
    }
  }

  Future<void> _saveJsonFile() async {
    final result = await SharedServiceRegistry().saveFile(
      dialogTitle: 'Save JSON file',
      fileName: 'data.json',
      type: FileType.custom,
      allowedExtensions: ['json'],
    );

    if (result != null) {
      // Here you would write your JSON content to the file
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Would save JSON to: $result')),
      );
    }
  }
}

/// Example showing file utility functions
class FileUtilitiesExample extends StatelessWidget {
  const FileUtilitiesExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('File Utilities Example')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'File Utility Functions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () => _demonstrateFileUtils(context),
              child: const Text('Demonstrate File Utils'),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Available Utility Functions:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            const Text('• fileExists(path) - Check if file exists'),
            const Text('• directoryExists(path) - Check if directory exists'),
            const Text('• getFileSize(path) - Get file size in bytes'),
            const Text('• getHumanReadableSize(bytes) - Format file size'),
            const Text('• generateUniqueFilename(path) - Avoid name conflicts'),
          ],
        ),
      ),
    );
  }

  void _demonstrateFileUtils(BuildContext context) {
    final registry = SharedServiceRegistry();
    final fileOps = registry.fileOperations;
    
    // Example file size formatting
    final sizes = [1024, 1048576, 1073741824];
    final formattedSizes = sizes.map((size) => 
        '${size} bytes = ${fileOps.getHumanReadableSize(size)}'
    ).join('\n');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('File Utilities Demo'),
        content: Text('File Size Examples:\n$formattedSizes'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
