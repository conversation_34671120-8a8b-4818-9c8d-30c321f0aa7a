# Jelly Pi Shared Functionality Developer Guide

## Quick Start

### 1. Import the Registry
```dart
import '../shared/shared_service_registry.dart';
```

### 2. Use Shared Services
```dart
// Icon Picker
final iconResult = await SharedServiceRegistry().showIconPicker(context);

// File Operations
final filePath = await SharedServiceRegistry().pickFile();

// Dialogs
final confirmed = await SharedServiceRegistry.showConfirmation(context, 
  title: 'Confirm', message: 'Are you sure?');

// Theme Colors
final statusColor = SharedServiceRegistry.getStatusColor('online');
```

## Available Services

### 🎨 Icon Picker Service
- **Purpose**: Unified icon selection with Material icons and custom images
- **Usage**: `SharedServiceRegistry().showIconPicker(context)`
- **Returns**: `Map<String, dynamic>` with `iconName` and `iconPath`
- **Example**: See `examples/icon_picker_example.dart`

### 📁 File Operations Service
- **Purpose**: File picking, saving, and directory operations
- **Methods**:
  - `pickFile()` - Single file selection
  - `pickFiles()` - Multiple file selection
  - `pickImage()` - Image file selection
  - `saveFile()` - File saving dialog
  - `pickDirectory()` - Directory selection
- **Example**: See `examples/file_operations_example.dart`

### 💬 Dialog Service
- **Purpose**: Standardized dialogs for user interactions
- **Methods**:
  - `showConfirmation()` - General confirmation
  - `showDeleteConfirmation()` - Delete confirmation with warning
  - `showInfo()` / `showSuccess()` / `showError()` - Information dialogs
  - `showTextInput()` - Text input with validation
  - `showLoading()` / `hideLoading()` - Loading indicators
- **Example**: See `examples/dialog_service_example.dart`

### 🎨 Theme Service
- **Purpose**: Consistent theming and color utilities
- **Methods**:
  - `getStatusColor()` - Colors for status indicators
  - `getActionColors()` - Action button colors
  - `createCardDecoration()` - Themed card styling
  - `getResponsiveColumns()` - Responsive grid layouts
- **Example**: See `examples/theme_service_example.dart`

## Best Practices

### ✅ Do's
- Always use shared services instead of implementing your own
- Handle null returns from async operations gracefully
- Use consistent theming through Theme Service
- Provide user feedback with progress notifications
- Follow established patterns from examples

### ❌ Don'ts
- Don't duplicate functionality that exists in shared services
- Don't ignore error handling in file operations
- Don't use hardcoded colors - use Theme Service
- Don't create custom dialogs when standard ones exist

## Common Patterns

### Icon Selection in Forms
```dart
InkWell(
  onTap: () async {
    final result = await SharedServiceRegistry().showIconPicker(context);
    if (result != null) {
      setState(() {
        _selectedIcon = result['iconName'];
        _selectedIconPath = result['iconPath'];
      });
    }
  },
  child: Row(
    children: [
      SharedServiceRegistry().buildIconWidget(_selectedIcon, _selectedIconPath),
      Text(_selectedIcon),
      Icon(Icons.arrow_drop_down),
    ],
  ),
)
```

### File Upload with Progress
```dart
Future<void> uploadFile() async {
  final filePath = await SharedServiceRegistry().pickFile();
  if (filePath == null) return;

  SharedServiceRegistry.showLoading(context, title: 'Uploading...');
  
  try {
    // Your upload logic here
    await performUpload(filePath);
    
    SharedServiceRegistry.hideLoading(context);
    await SharedServiceRegistry.showSuccess(context,
      title: 'Success', message: 'File uploaded successfully');
  } catch (e) {
    SharedServiceRegistry.hideLoading(context);
    await SharedServiceRegistry.showError(context,
      title: 'Upload Failed', message: e.toString());
  }
}
```

### Delete Confirmation
```dart
Future<void> deleteItem(String itemName) async {
  final confirmed = await SharedServiceRegistry.showDeleteConfirmation(
    context,
    itemName: itemName,
    warning: 'This action cannot be undone.',
  );

  if (confirmed) {
    // Perform deletion
    await performDelete(itemName);
  }
}
```

### Themed UI Components
```dart
Container(
  decoration: SharedServiceRegistry.createCardDecoration(context, 
    isSelected: isSelected),
  child: ListTile(
    leading: Icon(Icons.star, 
      color: SharedServiceRegistry.getStatusColor('active')),
    title: Text('Item'),
  ),
)
```

## Error Handling

### File Operations
```dart
try {
  final filePath = await SharedServiceRegistry().pickFile();
  if (filePath != null) {
    // Process file
  }
} catch (e) {
  await SharedServiceRegistry.showError(context,
    title: 'File Error', message: 'Failed to select file: $e');
}
```

### Icon Picker
```dart
final result = await SharedServiceRegistry().showIconPicker(context);
if (result != null) {
  // Icon selected successfully
  final iconName = result['iconName'] ?? 'apps';
  final iconPath = result['iconPath'];
} else {
  // User cancelled or error occurred
}
```

## Performance Tips

1. **Cache Icon Widgets**: Store icon widgets in variables if used multiple times
2. **Lazy Loading**: Only load file operations when needed
3. **Dispose Resources**: Always dispose controllers and streams
4. **Async Handling**: Use proper async/await patterns

## Migration Guide

### From Direct IconPickerDialog
```dart
// Old way
final result = await showDialog(
  context: context,
  builder: (context) => IconPickerDialog(selectedIcon: icon),
);

// New way
final result = await SharedServiceRegistry().showIconPicker(context,
  selectedIcon: icon);
```

### From FilePicker.platform
```dart
// Old way
final result = await FilePicker.platform.pickFiles();

// New way
final filePath = await SharedServiceRegistry().pickFile();
```

### From Custom Dialogs
```dart
// Old way
showDialog(context: context, builder: (context) => AlertDialog(...));

// New way
await SharedServiceRegistry.showConfirmation(context, 
  title: 'Title', message: 'Message');
```

## Contributing

When adding new shared functionality:

1. Create the service in `lib/shared/`
2. Add it to `SharedServiceRegistry`
3. Create examples in `examples/`
4. Update documentation
5. Test with existing apps

## Support

- Check examples in `examples/` directory
- Look at existing app implementations
- Refer to service documentation in source files
