{"repository": {"name": "<PERSON><PERSON>pp Hub Apt Repo", "version": "1.0.0", "description": "Repository for Raspberry Pi software packages with Apt Support", "source": "Official", "type": ["apt", "flatpak", "snap", "appimage"], "last_updated": "2025-06-06T10:47:17.246823"}, "categories": [{"id": "appearance", "name": "Appearance", "description": "Themes, fonts, and visual customization tools", "icon": "css"}, {"id": "creative", "name": "Creative Arts", "description": "Graphics, design, and creative applications", "icon": "brush"}, {"id": "engineering", "name": "Engineering", "description": "CAD, 3D printing, and engineering tools", "icon": "engineering"}, {"id": "games", "name": "Games", "description": "Gaming platforms, emulators, and entertainment", "icon": "sports_esports"}, {"id": "home_automation", "name": "Home Automation", "description": "Software for smart home control and automation", "icon": "home_iot"}, {"id": "internet", "name": "Internet", "description": "Network tools, download managers, and web utilities", "icon": "public"}, {"id": "browsers", "name": "Internet/Browsers", "description": "Web browsers and browsing tools", "icon": "web"}, {"id": "communication", "name": "Internet/Communication", "description": "Chat, messaging, and communication applications", "icon": "chat"}, {"id": "multimedia", "name": "Multimedia", "description": "Audio, video, and media editing applications", "icon": "movie"}, {"id": "networking", "name": "Networking", "description": "Network monitoring, analysis, and management tools", "icon": "router"}, {"id": "office", "name": "Office", "description": "Productivity, document editing, and office tools", "icon": "work"}, {"id": "programming", "name": "Programming", "description": "IDEs, development tools, and programming languages", "icon": "code"}, {"id": "servers", "name": "Servers", "description": "Web servers, file servers, and other server software", "icon": "dns"}, {"id": "system", "name": "System Management", "description": "System utilities, monitoring, and administration tools", "icon": "settings"}, {"id": "terminals", "name": "Terminals", "description": "Terminal emulators and command-line tools", "icon": "terminal"}, {"id": "tools", "name": "Tools", "description": "General utilities and productivity tools", "icon": "build"}, {"id": "crypto", "name": "Tools/Crypto", "description": "Cryptocurrency and blockchain tools", "icon": "currency_bitcoin"}, {"id": "emulation", "name": "Tools/Emulation", "description": "Emulation and virtualization tools", "icon": "computer"}], "applications": [{"id": "alacritty", "name": "Alacritty Terminal", "description": "Fast, cross-platform, OpenGL terminal emulator.", "version": "latest", "category": "terminals", "icon": "devices", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt install -y alacritty"], "remove_commands": ["sudo apt remove -y alacritty"], "check_installed": "which alacritty", "check_version": "alacritty --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y alacritty"], "tags": ["terminal", "emulator", "fast", "opengl"], "requires_reboot": false, "pi_only": false}, {"id": "apache2", "name": "Apache HTTP Server", "description": "Powerful, flexible, and widely-used open-source web server software.", "version": "latest", "category": "servers", "icon": "http", "size": "~15MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y apache2"], "remove_commands": ["sudo apt remove -y apache2"], "check_installed": "which apache2", "check_version": "apache2 -v | head -n 1", "update_commands": ["sudo apt update", "sudo apt upgrade -y apache2"], "tags": ["web server", "httpd", "hosting", "internet"], "requires_reboot": false, "pi_only": false}, {"id": "audacity", "name": "Audacity", "description": "Free, open source, cross-platform audio software for multi-track recording and editing.", "version": "latest", "category": "multimedia", "icon": "custom", "size": "~30MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y audacity"], "remove_commands": ["sudo apt remove -y audacity"], "check_installed": "which audacity", "check_version": "audacity --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y audacity"], "tags": ["audio editor", "recorder", "sound", "music production", "podcast"], "requires_reboot": false, "pi_only": false}, {"id": "caskaydia-cove-nf", "name": "Caskaydia Cove NF", "description": "Nerd Font version of Cascadia Code with programming ligatures and symbols", "version": "latest", "category": "appearance", "icon": "font_download", "size": "~5MB", "install_type": "script", "install_commands": ["wget -O /tmp/CascadiaCode.zip https://github.com/ryanoasis/nerd-fonts/releases/latest/download/CascadiaCode.zip", "sudo unzip -o /tmp/CascadiaCode.zip -d /usr/share/fonts/truetype/", "sudo fc-cache -fv", "rm /tmp/CascadiaCode.zip"], "remove_commands": ["sudo rm -f /usr/share/fonts/truetype/CaskaydiaCove*.ttf", "sudo fc-cache -fv"], "check_installed": "fc-list | grep -i caskaydia", "check_version": "fc-list | grep -i caskaydia | head -1", "tags": ["font", "programming", "nerd-font"], "requires_reboot": false, "pi_only": false}, {"id": "chromium-browser", "name": "Chrom<PERSON> Browser", "description": "Open-source web browser project that aims to build a safer, faster, and more stable way for all Internet users to experience the web.", "version": "latest", "category": "browsers", "icon": "webhook", "size": "~0.0MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y chromium-browser"], "remove_commands": ["sudo apt remove -y chromium-browser"], "check_installed": "which chromium-browser", "check_version": "chromium-browser --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y chromium-browser"], "tags": ["browser", "web", "internet", "open source"], "requires_reboot": false, "pi_only": false}, {"id": "color-emoji-font", "name": "Color Emoji Font", "description": "Install Noto color emoji fonts for better emoji support in applications.", "version": "latest", "category": "appearance", "icon": "emoji_emotions", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y fonts-noto-color-emoji", "sudo fc-cache -fv"], "remove_commands": ["sudo apt remove -y fonts-noto-color-emoji", "sudo fc-cache -fv"], "check_installed": "fc-list | grep -i 'Noto Color Emoji'", "tags": ["font", "emoji", "unicode"], "requires_reboot": false, "pi_only": false}, {"id": "colored-man-pages", "name": "Colored Man Pages", "description": "Add syntax highlighting and colors to manual pages for better readability using 'most'.", "version": "latest", "category": "appearance", "icon": "colorize", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y most", "echo 'export PAGER=most' >> ~/.bashrc"], "remove_commands": ["sudo apt remove -y most", "sed -i '/export PAGER=most/d' ~/.bashrc"], "check_installed": "which most", "tags": ["terminal", "colors", "documentation", "pager"], "requires_reboot": false, "pi_only": false}, {"id": "conky", "name": "<PERSON><PERSON>", "description": "Lightweight system monitor for X that displays system information on desktop.", "version": "latest", "category": "appearance", "icon": "monitor", "size": "~5MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y conky-all"], "remove_commands": ["sudo apt remove -y conky-all"], "check_installed": "which conky", "check_version": "conky --version | head -1", "update_commands": ["sudo apt update", "sudo apt upgrade -y conky-all"], "tags": ["system monitor", "desktop", "widgets"], "requires_reboot": false, "pi_only": false}, {"id": "docker", "name": "<PERSON>er", "description": "Platform for developing, shipping, and running applications in containers.", "version": "latest", "category": "emulation", "icon": "developer_board", "size": "~200MB", "install_type": "script", "install_commands": ["curl -fsSL https://get.docker.com -o get-docker.sh", "sudo sh get-docker.sh", "sudo usermod -aG docker $USER", "rm get-docker.sh"], "remove_commands": ["sudo apt-get purge -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin docker-ce-rootless-extras", "sudo rm -rf /var/lib/docker", "sudo rm -rf /var/lib/containerd"], "check_installed": "docker --version", "check_version": "docker --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y docker-ce docker-ce-cli containerd.io"], "tags": ["containers", "virtualization", "development", "microservices"], "requires_reboot": true, "pi_only": false}, {"id": "filezilla-client", "name": "FileZilla Client", "description": "Free FTP solution. Both a client and a server are available.", "version": "latest", "category": "internet", "icon": "folder_open", "size": "~20MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y filezilla"], "remove_commands": ["sudo apt remove -y filezilla"], "check_installed": "which filezilla", "check_version": "filezilla --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y filezilla"], "tags": ["ftp", "sftp", "ftps", "file transfer", "client"], "requires_reboot": false, "pi_only": false}, {"id": "firefox", "name": "Firefox ESR", "description": "Fast, private and secure web browser from Mozilla (Extended Support Release).", "version": "latest-esr", "category": "browsers", "icon": "public", "size": "~100MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y firefox-esr"], "remove_commands": ["sudo apt remove -y firefox-esr"], "check_installed": "which firefox-esr || which firefox", "check_version": "firefox-esr --version || firefox --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y firefox-esr"], "tags": ["browser", "web", "mozilla", "internet"], "requires_reboot": false, "pi_only": false}, {"id": "gimp", "name": "GIMP", "description": "GNU Image Manipulation Program - powerful image editor.", "version": "latest", "category": "creative", "icon": "image", "size": "~200MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y gimp"], "remove_commands": ["sudo apt remove -y gimp"], "check_installed": "which gimp", "check_version": "gimp --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y gimp"], "tags": ["graphics", "image editing", "design", "photo editor"], "requires_reboot": false, "pi_only": false}, {"id": "gparted", "name": "GParted", "description": "Free partition editor for graphically managing your disk partitions.", "version": "latest", "category": "system", "icon": "disc_full", "size": "~5MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y gparted"], "remove_commands": ["sudo apt remove -y gparted"], "check_installed": "which gparted", "check_version": "gparted --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y gparted"], "tags": ["partition manager", "disk utility", "system tool", "formatting"], "requires_reboot": false, "pi_only": false}, {"id": "grafana", "name": "<PERSON><PERSON>", "description": "The open observability platform for monitoring and visualizing data.", "version": "latest", "category": "system", "icon": "insights", "size": "~150MB", "install_type": "apt", "install_commands": ["sudo apt-get install -y apt-transport-https software-properties-common wget", "sudo wget -q -O /usr/share/keyrings/grafana.key https://apt.grafana.com/gpg.key", "echo \"deb [signed-by=/usr/share/keyrings/grafana.key] https://apt.grafana.com stable main\" | sudo tee -a /etc/apt/sources.list.d/grafana.list", "sudo apt-get update", "sudo apt-get install -y grafana"], "remove_commands": ["sudo apt-get remove -y grafana", "sudo rm /etc/apt/sources.list.d/grafana.list"], "check_installed": "which grafana-server", "check_version": "grafana-server -v | awk '{print $2}'", "update_commands": ["sudo apt-get update", "sudo apt-get upgrade -y grafana"], "tags": ["monitoring", "dashboard", "visualization", "metrics", "observability"], "requires_reboot": false, "pi_only": false}, {"id": "home-assistant", "name": "Home Assistant", "description": "Open source home automation that puts local control and privacy first.", "version": "latest", "category": "home_automation", "icon": "home", "size": "~500MB (includes dependencies)", "install_type": "docker", "install_commands": ["sudo docker run -d --name homeassistant --privileged --restart=unless-stopped -e TZ=YOUR_TIME_ZONE -v /PATH_TO_YOUR_CONFIG:/config --network=host ghcr.io/home-assistant/home-assistant:stable", "echo \"Remember to replace YOUR_TIME_ZONE and /PATH_TO_YOUR_CONFIG\""], "remove_commands": ["sudo docker stop homeassistant", "sudo docker rm homeassistant"], "check_installed": "sudo docker ps -a | grep homeassistant", "check_version": "sudo docker inspect ghcr.io/home-assistant/home-assistant:stable | grep version", "tags": ["smart home", "automation", "iot", "local control", "privacy"], "requires_reboot": false, "pi_only": false}, {"id": "homebridge", "name": "Homebridge", "description": "Emulates the iOS HomeKit API, allowing you to control non-HomeKit devices.", "version": "latest", "category": "home_automation", "icon": "settings_input_component", "size": "~100MB (plus Node.js dependencies)", "install_type": "script", "install_commands": ["sudo apt update", "sudo apt install -y nodejs npm", "sudo npm install -g --unsafe-perm homebridge homebridge-config-ui-x", "echo \"Run 'homebridge' or setup as a service. Access UI at http://<pi-ip>:8581\""], "remove_commands": ["sudo npm uninstall -g homebridge homebridge-config-ui-x"], "check_installed": "which homebridge", "check_version": "homebridge --version", "update_commands": ["sudo npm update -g homebridge homebridge-config-ui-x"], "tags": ["homekit", "iot", "smart home", "apple", "automation"], "requires_reboot": false, "pi_only": false}, {"id": "inkscape", "name": "Inkscape", "description": "Professional quality vector graphics software which runs on Linux, Mac OS X and Windows.", "version": "latest", "category": "creative", "icon": "gesture", "size": "~100MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y inkscape"], "remove_commands": ["sudo apt remove -y inkscape"], "check_installed": "which inkscape", "check_version": "inkscape --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y inkscape"], "tags": ["vector graphics", "svg", "design", "illustration", "drawing"], "requires_reboot": false, "pi_only": false}, {"id": "jellyfin", "name": "<PERSON><PERSON><PERSON>", "description": "The Free Software Media System that puts you in control of managing and streaming your media.", "version": "latest", "category": "servers", "icon": "ondemand_video", "size": "~120MB", "install_type": "script", "install_commands": ["sudo apt install -y apt-transport-https", "curl https://repo.jellyfin.org/debian/jellyfin_team.gpg.key | sudo apt-key add -", "echo \"deb [arch=$( dpkg --print-architecture )] https://repo.jellyfin.org/debian $( lsb_release -c -s ) main\" | sudo tee /etc/apt/sources.list.d/jellyfin.list", "sudo apt update", "sudo apt install -y jellyfin"], "remove_commands": ["sudo apt remove -y jellyfin", "sudo rm /etc/apt/sources.list.d/jellyfin.list"], "check_installed": "which jellyfin", "check_version": "jellyfin --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y jellyfin"], "tags": ["media server", "streaming", "video", "music", "open source"], "requires_reboot": false, "pi_only": false}, {"id": "kicad", "name": "KiCad", "description": "Open source electronics design automation suite for PCB design.", "version": "latest", "category": "engineering", "icon": "electrical_services", "size": "~500MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y kicad"], "remove_commands": ["sudo apt remove -y kicad"], "check_installed": "which kicad", "check_version": "kicad --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y kicad"], "tags": ["pcb", "electronics", "cad", "eda"], "requires_reboot": false, "pi_only": false}, {"id": "kodi", "name": "<PERSON><PERSON>", "description": "Open source home theater software for a wide range of media.", "version": "latest", "category": "multimedia", "icon": "live_tv", "size": "~100MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y kodi"], "remove_commands": ["sudo apt remove -y kodi kodi-bin"], "check_installed": "which kodi", "check_version": "kodi --version | grep Kodi | awk '{print $2}'", "update_commands": ["sudo apt update", "sudo apt upgrade -y kodi"], "tags": ["media center", "home theater", "video", "music", "htpc"], "requires_reboot": false, "pi_only": false}, {"id": "discord-legcord", "name": "Legcord", "description": "Lightweight Discord client with better privacy and customization.", "version": "latest", "category": "communication", "icon": "chat_bubble", "size": "~80MB", "install_type": "script", "install_commands": ["wget -O /tmp/legcord.deb $(curl -s https://api.github.com/repos/Legcord/Legcord/releases/latest | grep browser_download_url | grep arm64.deb | cut -d '\"' -f 4)", "sudo dpkg -i /tmp/legcord.deb", "sudo apt install -f -y", "rm /tmp/legcord.deb"], "remove_commands": ["sudo apt remove -y legcord"], "check_installed": "which legcord", "tags": ["discord", "chat", "communication", "messaging"], "requires_reboot": false, "pi_only": false}, {"id": "libreoffice", "name": "LibreOffice", "description": "Free and open source office suite with word processor, spreadsheet, and presentation tools.", "version": "latest", "category": "office", "icon": "description", "size": "~400MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y libreoffice"], "remove_commands": ["sudo apt remove -y libreoffice*"], "check_installed": "which libreoffice", "check_version": "libreoffice --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y libreoffice"], "tags": ["office", "documents", "productivity", "writer", "calc", "impress"], "requires_reboot": false, "pi_only": false}, {"id": "magicmirror", "name": "MagicMirror²", "description": "Open source modular smart mirror platform.", "version": "latest", "category": "home_automation", "icon": "auto_awesome", "size": "~200MB (plus Node.js dependencies)", "install_type": "script", "install_commands": ["bash -c \"$(curl -sL https://raw.githubusercontent.com/MichMich/MagicMirror/master/installers/raspberry.sh)\""], "remove_commands": ["echo \"Remove MagicMirror directory. Uninstall Node.js if not needed.\""], "check_installed": "test -d ~/MagicMirror", "check_version": "cd ~/MagicMirror && git describe --tags 2>/dev/null || echo 'N/A'", "tags": ["smart mirror", "information display", "modular", "nodejs"], "requires_reboot": false, "pi_only": false}, {"id": "minecraft-java-prism", "name": "Minecraft Java Prism Launcher", "description": "Advanced Minecraft launcher with mod support and multiple instances.", "version": "latest", "category": "games", "icon": "sports_esports", "size": "~50MB", "install_type": "script", "install_commands": ["wget -O /tmp/PrismLauncher.AppImage https://github.com/PrismLauncher/PrismLauncher/releases/latest/download/PrismLauncher-Linux-arm64.AppImage", "chmod +x /tmp/PrismLauncher.AppImage", "sudo mv /tmp/PrismLauncher.AppImage /usr/local/bin/prismlauncher", "echo '[Desktop Entry]\\nName=Prism Launcher\\nExec=/usr/local/bin/prismlauncher\\nIcon=prismlauncher\\nType=Application\\nCategories=Game;' | sudo tee /usr/share/applications/prismlauncher.desktop", "sudo apt install -y fuse"], "remove_commands": ["sudo rm -f /usr/local/bin/prismlauncher", "sudo rm -f /usr/share/applications/prismlauncher.desktop"], "check_installed": "test -f /usr/local/bin/prismlauncher", "tags": ["minecraft", "gaming", "launcher", "java"], "requires_reboot": false, "pi_only": false}, {"id": "motioneye", "name": "MotionEye", "description": "A web-based frontend for the motion daemon. Turns your Pi into a video surveillance system.", "version": "latest", "category": "system", "icon": "videocam", "size": "~50MB (plus dependencies)", "install_type": "pip", "install_commands": ["sudo apt-get install -y python3-pip python3-dev libssl-dev libcurl4-openssl-dev libjpeg-dev libz-dev ffmpeg motion v4l-utils", "sudo pip3 install motioneye", "sudo mkdir -p /etc/motioneye", "sudo cp /usr/local/share/motioneye/extra/motioneye.conf.sample /etc/motioneye/motioneye.conf", "sudo mkdir -p /var/lib/motioneye", "echo \"Run 'sudo meyectl startserver -c /etc/motioneye/motioneye.conf' or setup as service\""], "remove_commands": ["sudo pip3 uninstall motioneye", "sudo apt-get remove -y motion ffmpeg"], "check_installed": "which meyectl", "check_version": "meyectl --version | head -n 1", "tags": ["cctv", "surveillance", "camera", "video monitoring", "security"], "requires_reboot": false, "pi_only": false}, {"id": "neofetch", "name": "Neofetch", "description": "A command-line system information tool written in bash 3.2+.", "version": "latest", "category": "system", "icon": "info", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y neofetch"], "remove_commands": ["sudo apt remove -y neofetch"], "check_installed": "which neofetch", "check_version": "neofetch --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y neofetch"], "tags": ["system info", "cli", "terminal", "customization"], "requires_reboot": false, "pi_only": false}, {"id": "nextcloud", "name": "Nextcloud", "description": "A suite of client-server software for creating and using file hosting services.", "version": "latest", "category": "servers", "icon": "cloud_circle", "size": "~300MB (server, depends on apps)", "install_type": "docker", "install_commands": ["echo \"Visit Nextcloud documentation for various install methods (Docker, Snap, manual).\"", "echo \"Example Docker: sudo docker run -d -p 8080:80 nextcloud\""], "remove_commands": ["echo \"Removal depends on installation method.\""], "check_installed": "docker ps | grep nextcloud || false", "check_version": "echo \"Check depends on installation method.\"", "tags": ["cloud storage", "file sync", "collaboration", "self-hosted"], "requires_reboot": false, "pi_only": false}, {"id": "nginx", "name": "<PERSON><PERSON><PERSON>", "description": "High-performance HTTP server, reverse proxy, and IMAP/POP3 proxy server.", "version": "latest", "category": "servers", "icon": "language", "size": "~10MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y nginx"], "remove_commands": ["sudo apt remove -y nginx"], "check_installed": "which nginx", "check_version": "nginx -v", "update_commands": ["sudo apt update", "sudo apt upgrade -y nginx"], "tags": ["web server", "reverse proxy", "load balancer", "performance"], "requires_reboot": false, "pi_only": false}, {"id": "node-red", "name": "Node-RED", "description": "Low-code programming for event-driven applications. Browser-based flow editing.", "version": "latest", "category": "programming", "icon": "account_tree", "size": "~100MB", "install_type": "script", "install_commands": ["bash <(curl -sL https://raw.githubusercontent.com/node-red/linux-installers/master/deb/update-nodejs-and-nodered)"], "remove_commands": ["sudo npm remove -g --unsafe-perm node-red node-red-admin", "sudo rm -R ~/.node-red"], "check_installed": "which node-red", "check_version": "node-red --version", "update_commands": ["bash <(curl -sL https://raw.githubusercontent.com/node-red/linux-installers/master/deb/update-nodejs-and-nodered)"], "tags": ["iot", "automation", "visual programming", "flow-based"], "requires_reboot": false, "pi_only": false}, {"id": "nodejs", "name": "Node.js", "description": "JavaScript runtime built on Chrome's V8 JavaScript engine.", "version": "20.x", "category": "programming", "icon": "javascript", "size": "~50MB", "install_type": "script", "install_commands": ["curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -", "sudo apt install -y nodejs"], "remove_commands": ["sudo apt remove -y nodejs npm", "sudo rm -f /etc/apt/sources.list.d/nodesource.list"], "check_installed": "node --version", "check_version": "node --version", "update_commands": ["curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -", "sudo apt update", "sudo apt upgrade -y nodejs"], "tags": ["javascript", "development", "runtime", "server"], "requires_reboot": false, "pi_only": false}, {"id": "obs-studio", "name": "OBS Studio", "description": "Free and open source software for video recording and live streaming.", "version": "latest", "category": "multimedia", "icon": "videocam", "size": "~150MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y obs-studio"], "remove_commands": ["sudo apt remove -y obs-studio"], "check_installed": "which obs", "check_version": "obs --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y obs-studio"], "tags": ["streaming", "recording", "video", "screen capture"], "requires_reboot": false, "pi_only": false}, {"id": "octoprint", "name": "OctoPrint", "description": "The snappy web interface for your 3D printer.", "version": "latest", "category": "engineering", "icon": "3d_rotation", "size": "~100MB (plus Python dependencies)", "install_type": "pip", "install_commands": ["sudo apt update", "sudo apt install -y python3-pip python3-dev python3-setuptools python3-venv git libyaml-dev build-essential", "mkdir OctoPrint && cd OctoPrint", "python3 -m venv venv", "source venv/bin/activate", "pip install octoprint", "echo \"Run 'octoprint serve' to start. Consider setting up as a service.\""], "remove_commands": ["echo \"Deactivate venv and remove OctoPrint directory. If installed as service, disable/remove service.\""], "check_installed": "test -f ~/OctoPrint/venv/bin/octoprint", "check_version": "~/OctoPrint/venv/bin/octoprint --version", "tags": ["3d printing", "web interface", "printer control", "gcode"], "requires_reboot": false, "pi_only": false}, {"id": "openmediavault", "name": "OpenMediaVault", "description": "Next generation network attached storage (NAS) solution based on Debian Linux.", "version": "latest", "category": "servers", "icon": "storage", "size": "~100MB (base system)", "install_type": "script", "install_commands": ["curl -sSL https://github.com/OpenMediaVault-Plugin-Developers/installScript/raw/master/install | sudo bash"], "remove_commands": ["echo \"OMV deeply integrates with the system. Re-imaging is often recommended for full removal.\""], "check_installed": "dpkg -s openmediavault", "check_version": "dpkg -s openmediavault | grep Version | awk '{print $2}'", "tags": ["nas", "storage", "file server", "dlna", "plex", "network storage"], "requires_reboot": true, "pi_only": false}, {"id": "pi-hole", "name": "Pi-hole", "description": "A network-wide ad blocker and DNS server.", "version": "latest", "category": "networking", "icon": "block", "size": "~50MB", "install_type": "script", "install_commands": ["curl -sSL https://install.pi-hole.net | bash"], "remove_commands": ["pihole uninstall"], "check_installed": "which pihole", "check_version": "pihole version", "update_commands": ["pihole -up"], "tags": ["ad blocker", "dns", "network", "privacy"], "requires_reboot": false, "pi_only": false}, {"id": "plex-media-server", "name": "Plex Media Server", "description": "Organizes your video, music, and photo collections and streams them to your devices.", "version": "latest", "category": "servers", "icon": "personal_video", "size": "~150MB", "install_type": "script", "install_commands": ["curl https://downloads.plex.tv/plex-keys/PlexSign.key | sudo apt-key add -", "echo deb https://downloads.plex.tv/repo/deb ./public main | sudo tee /etc/apt/sources.list.d/plexmediaserver.list", "sudo apt update", "sudo apt install -y plexmediaserver"], "remove_commands": ["sudo apt remove -y plexmediaserver", "sudo rm /etc/apt/sources.list.d/plexmediaserver.list"], "check_installed": "dpkg -s plexmediaserver", "check_version": "dpkg -s plexmediaserver | grep Version | awk '{print $2}'", "update_commands": ["sudo apt update", "sudo apt upgrade -y plexmediaserver"], "tags": ["media server", "streaming", "video", "music", "photos"], "requires_reboot": false, "pi_only": false}, {"id": "portainer-ce", "name": "Portainer CE", "description": "Lightweight management UI which allows you to easily manage your Docker environments.", "version": "latest", "category": "emulation", "icon": "widgets", "size": "~20MB (Docker image)", "install_type": "docker", "install_commands": ["sudo docker volume create portainer_data", "sudo docker run -d -p 8000:8000 -p 9443:9443 --name portainer --restart=always -v /var/run/docker.sock:/var/run/docker.sock -v portainer_data:/data portainer/portainer-ce:latest"], "remove_commands": ["sudo docker stop portainer", "sudo docker rm portainer", "sudo docker volume rm portainer_data"], "check_installed": "sudo docker ps -a | grep portainer", "check_version": "sudo docker inspect portainer/portainer-ce:latest | grep VERSION -m 1", "tags": ["docker", "container management", "gui", "orchestration"], "requires_reboot": false, "pi_only": false}, {"id": "prometheus", "name": "Prometheus", "description": "Open-source systems monitoring and alerting toolkit.", "version": "latest", "category": "system", "icon": "query_stats", "size": "~50MB", "install_type": "script", "install_commands": ["echo \"Download latest arm64 release from https://prometheus.io/download/\"", "echo \"Example: wget https://github.com/prometheus/prometheus/releases/download/vX.Y.Z/prometheus-X.Y.Z.linux-arm64.tar.gz\"", "echo \"tar xvfz prometheus-*.tar.gz && cd prometheus-* && sudo cp prometheus promtool /usr/local/bin/\""], "remove_commands": ["sudo rm /usr/local/bin/prometheus /usr/local/bin/promtool"], "check_installed": "which prometheus", "check_version": "prometheus --version", "tags": ["monitoring", "alerting", "metrics", "time series", "observability"], "requires_reboot": false, "pi_only": false}, {"id": "rpi-imager", "name": "Raspberry Pi Imager", "description": "An easy way to install Raspberry Pi OS and other operating systems to an SD card.", "version": "latest", "category": "system", "icon": "sd_storage", "size": "~20MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y rpi-imager"], "remove_commands": ["sudo apt remove -y rpi-imager"], "check_installed": "which rpi-imager", "check_version": "apt show rpi-imager | grep Version | awk '{print $2}'", "update_commands": ["sudo apt update", "sudo apt upgrade -y rpi-imager"], "tags": ["sd card", "os installer", "imaging", "raspberry pi os"], "requires_reboot": false, "pi_only": false}, {"id": "retropie", "name": "RetroPie", "description": "Software library to emulate retro video games on the Raspberry Pi.", "version": "latest", "category": "games", "icon": "sports_esports", "size": "~2GB (with some emulators/themes)", "install_type": "script", "install_commands": ["sudo apt update && sudo apt upgrade -y", "sudo apt install -y git lsb-release", "git clone --depth=1 https://github.com/RetroPie/RetroPie-Setup.git", "cd RetroPie-Setup", "sudo ./retropie_setup.sh"], "remove_commands": ["cd RetroPie-Setup", "sudo ./retropie_setup.sh", "echo \"Then choose Uninstall from the menu\""], "check_installed": "test -d ~/RetroPie-Setup && test -d /opt/retropie", "check_version": "cat /opt/retropie/VERSION 2>/dev/null || echo 'N/A'", "tags": ["gaming", "emulator", "retro", "console"], "requires_reboot": true, "pi_only": false}, {"id": "samba", "name": "Samba", "description": "Standard Windows interoperability suite of programs for Linux and Unix (File Sharing).", "version": "latest", "category": "servers", "icon": "folder_shared", "size": "~20MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y samba samba-common-bin"], "remove_commands": ["sudo apt remove -y samba samba-common-bin"], "check_installed": "which smbd", "check_version": "smbd --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y samba samba-common-bin"], "tags": ["file server", "smb", "cifs", "windows share", "networking"], "requires_reboot": false, "pi_only": false}, {"id": "syncthing", "name": "Syncthing", "description": "Continuous file synchronization program. It synchronizes files between two or more computers.", "version": "latest", "category": "tools", "icon": "sync", "size": "~20MB", "install_type": "script", "install_commands": ["sudo curl -s -o /usr/share/keyrings/syncthing-archive-keyring.gpg https://syncthing.net/release-key.gpg", "echo \"deb [signed-by=/usr/share/keyrings/syncthing-archive-keyring.gpg] https://apt.syncthing.net/ syncthing stable\" | sudo tee /etc/apt/sources.list.d/syncthing.list", "sudo apt update", "sudo apt install syncthing"], "remove_commands": ["sudo apt remove syncthing", "sudo rm /etc/apt/sources.list.d/syncthing.list"], "check_installed": "which syncthing", "check_version": "syncthing --version", "update_commands": ["sudo apt update", "sudo apt upgrade syncthing"], "tags": ["file sync", "backup", "peer-to-peer", "decentralized"], "requires_reboot": false, "pi_only": false}, {"id": "thonny", "name": "Thonny Python IDE", "description": "Python IDE for beginners. Simple interface, debugger, and step-through execution.", "version": "latest", "category": "programming", "icon": "integration_instructions", "size": "~50MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y thonny"], "remove_commands": ["sudo apt remove -y thonny"], "check_installed": "which thonny", "check_version": "thonny --version", "update_commands": ["sudo apt update", "sudo apt upgrade -y thonny"], "tags": ["python", "ide", "education", "beginner", "development"], "requires_reboot": false, "pi_only": false}, {"id": "transmission-cli", "name": "Transmission (CLI/Daemon)", "description": "A lightweight BitTorrent client, with daemon and CLI tools.", "version": "latest", "category": "internet", "icon": "download_for_offline", "size": "~5MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y transmission-daemon transmission-cli"], "remove_commands": ["sudo apt remove -y transmission-daemon transmission-cli"], "check_installed": "which transmission-daemon", "check_version": "transmission-daemon --version | head -n 1", "update_commands": ["sudo apt update", "sudo apt upgrade -y transmission-daemon transmission-cli"], "tags": ["bittorrent", "p2p", "downloader", "daemon", "cli"], "requires_reboot": false, "pi_only": false}, {"id": "vlc", "name": "VLC Media Player", "description": "Free and open source cross-platform multimedia player and framework.", "version": "latest", "category": "multimedia", "icon": "play_circle_filled", "size": "~50MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y vlc"], "remove_commands": ["sudo apt remove -y vlc"], "check_installed": "which vlc", "check_version": "vlc --version | head -n 1 | awk '{print $3}'", "update_commands": ["sudo apt update", "sudo apt upgrade -y vlc"], "tags": ["media player", "video", "audio", "streaming", "cross-platform"], "requires_reboot": false, "pi_only": false}, {"id": "vscode", "name": "Visual Studio Code", "description": "Lightweight but powerful source code editor with IntelliSense and debugging.", "version": "latest", "category": "programming", "icon": "code", "size": "~200MB", "install_type": "script", "install_commands": ["wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg", "sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/", "echo 'deb [arch=arm64 signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main' | sudo tee /etc/apt/sources.list.d/vscode.list", "sudo apt update", "sudo apt install -y code"], "remove_commands": ["sudo apt remove -y code", "sudo rm -f /etc/apt/sources.list.d/vscode.list", "sudo rm -f /etc/apt/trusted.gpg.d/packages.microsoft.gpg"], "check_installed": "which code", "check_version": "code --version | head -n 1", "update_commands": ["sudo apt update", "sudo apt upgrade -y code"], "tags": ["ide", "editor", "development", "programming"], "requires_reboot": false, "pi_only": false}, {"id": "htop", "name": "htop", "description": "Interactive process viewer and system monitor for the terminal.", "version": "latest", "category": "system", "icon": "monitor_heart", "size": "~1MB", "install_type": "apt", "install_commands": ["sudo apt update", "sudo apt install -y htop"], "remove_commands": ["sudo apt remove -y htop"], "check_installed": "which htop", "check_version": "htop --version | head -n 1", "update_commands": ["sudo apt update", "sudo apt upgrade -y htop"], "tags": ["monitoring", "system", "processes", "terminal"], "requires_reboot": false, "pi_only": false}]}